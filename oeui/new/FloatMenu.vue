<template>
  <view class="float-menu-container" ref="container">
    <view class="float-trigger" @click.stop="toggleMenu" ref="trigger">
      <slot name="trigger"></slot>
    </view>

    <view v-show="isVisible" class="float-menu" :class="{ 'float-menu-top': position === 'top' }" :style="menuStyle" ref="menu">
      <view class="float-menu-arrow" :style="arrowStyle"></view>
      <slot name="menu">
        <view class="float-menu-item flex flex_dc" v-for="(item, index) in items" :key="index" @click="handleItemClick(item)">
          {{ item }}
        </view>
      </slot>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue';

const props = defineProps({
  items: {
    type: Array,
    default: () => []
  },
  position: {
    type: String,
    default: 'top',
    validator: value => ['top', 'bottom'].includes(value)
  },
  sameWidth: {
    type: Boolean,
    default: true
  },
  offsetY: {
    type: Number,
    default: 20
  },
  // 新增箭头偏移量
  arrowOffset: {
    type: Number,
    default: -4
  },
  operateDisabled: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['open', 'close', 'select']);

const isVisible = ref(false);
const trigger = ref(null);
const menu = ref(null);
const container = ref(null);
const triggerRect = ref({});
const menuRect = ref({});

// 计算菜单样式
const menuStyle = computed(() => {
  const style = {
    'z-index': 1001,
    position: 'absolute',
    left: '50%',
    transform: 'translateX(-50%)',
    'min-width': '170rpx'
  };

  if (props.sameWidth && triggerRect.value.width) {
    style.width = `${triggerRect.value.width}px`;
  }

  if (props.position === 'top') {
    style.bottom = `calc(100% + ${props.offsetY}px)`;
  } else {
    style.top = `calc(100% + ${props.offsetY}px)`;
  }

  return style;
});

// 计算箭头样式
const arrowStyle = computed(() => {
  return {
    left: `calc(50% + ${props.arrowOffset}px)`
  };
});

const toggleMenu = async () => {
  if (props.operateDisabled) return;
  isVisible.value = !isVisible.value;

  if (isVisible.value) {
    emit('open');
    await nextTick();
    updateRects();
    adjustPosition();
  } else {
    emit('close');
  }
};

const closeMenu = () => {
  if (isVisible.value) {
    isVisible.value = false;
    emit('close');
  }
};

const handleItemClick = item => {
  emit('select', item);
  closeMenu();
};

// 更新触发元素和菜单的尺寸信息
const updateRects = () => {
  const query = uni.createSelectorQuery();

  query.select('.float-trigger').boundingClientRect(data => {
    if (data) {
      triggerRect.value = data;
    }
  });

  query
    .select('.float-menu')
    .boundingClientRect(data => {
      if (data) {
        menuRect.value = data;
      }
    })
    .exec();
};

// 调整菜单位置
const adjustPosition = () => {
  const systemInfo = uni.getSystemInfoSync();
  const windowWidth = systemInfo.windowWidth;

  if (!menu.value) return;

  const menuElement = menu.value.$el || menu.value;

  // 检查左侧是否超出
  if (menuRect.value.left < 0) {
    menuElement.style.left = '0';
    menuElement.style.transform = 'translateX(0)';
  }
  // 检查右侧是否超出
  else if (menuRect.value.right > windowWidth) {
    menuElement.style.left = 'auto';
    menuElement.style.right = '0';
    menuElement.style.transform = 'translateX(0)';
  }
};

const handleClickOutside = event => {
  const containerElement = container.value?.$el || container.value;
  if (isVisible.value && containerElement && !containerElement.contains(event.target)) {
    closeMenu();
  }
};

const handleKeyDown = event => {
  if (event.key === 'Escape' && isVisible.value) {
    closeMenu();
  }
};

onMounted(() => {
  if (typeof document !== 'undefined') {
    document.addEventListener('click', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);
  }
});

onBeforeUnmount(() => {
  if (typeof document !== 'undefined') {
    document.removeEventListener('click', handleClickOutside);
    document.removeEventListener('keydown', handleKeyDown);
  }
});

defineExpose({
  closeMenu
});
</script>

<style scoped>
.float-menu-container {
  position: relative;
  display: inline-block;
}

.float-trigger {
  cursor: pointer;
  border-radius: 8rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.float-menu {
  position: absolute;
  background: #ffffff;
  border-radius: 16rpx;
  overflow: visible;
  animation: float-show 0.2s ease-out;
  transform-origin: center;
  box-shadow: 0px 12rpx 20rpx 0px rgba(0, 8, 182, 0.1);
}

.float-menu-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-left: 10rpx solid transparent;
  border-right: 10rpx solid transparent;
  border-bottom: 10rpx solid #fff;
}

.float-menu-top .float-menu-arrow {
  top: auto;
  bottom: -8rpx;
  border-bottom: none;
  border-top: 10rpx solid #fff;
}

.float-menu-item {
  height: 84rpx;
  font-size: 28rpx;
  color: #2c5ce1;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.2s;
}

.float-menu-item:last-child {
  border-bottom: none;
}

.float-menu-item:hover {
  background-color: #f7f7f9;
}

.float-menu-item:active {
  background-color: #ecf5ff;
}

@keyframes float-show {
  0% {
    opacity: 0;
    transform: translate(-50%, 10rpx);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}
</style>

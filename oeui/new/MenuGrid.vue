<template>
  <view class="menu_row" v-if="filteredMenu.length">
    <view class="menu_item" v-for="(item, index) in filteredMenu" :key="index" @click="navigateToPage(item)">
      <view class="icon_wrapper" :style="{ backgroundColor: item.color }">
        <template v-if="shouldShowBadge(item)">
          <uni-badge :text="getBadgeText(item)" absolute="rightTop" :offset="getBadgeOffset(item)" :customStyle="{ background: '#F53F3F' }">
            <text class="iconfont" :class="item.icon"></text>
          </uni-badge>
        </template>
        <text v-else class="iconfont" :class="item.icon"></text>
      </view>
      <text class="menu_text">{{ item.text }}</text>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { usePermission } from '@/composables/usePermission';
import { initAssistant } from '@/api/common.js';

const { hasPermission } = usePermission();

// 菜单项
const MENU_CONFIG = Object.freeze([
  { icon: 'icon-fenpei', text: '待分配', mod: 'allot', power: 'allot_view', color: '#53B996', path: '/pages/sale/allotblank' },
  { icon: 'icon-duoren', text: '我的资源', path: '/pages/sale/mymember', mod: 'sale', power: 'sale_view', color: '#5BBBC2' },
  { icon: 'icon-zhuguanmiantan', text: '我的面谈', mod: 'faceto', power: 'faceto_view', color: '#62B6EA', path: '/pages/sale/inter_face' },
  { icon: 'icon-kefu', text: '我的协助', mod: 'sale', power: 'sale_view', color: '#51A1EB' },
  { icon: 'icon-shuju', text: '到店预测', mod: 'inter', power: 'inter_view', color: '#4ABBE1', path: '/pages/sale/inter' },
  { icon: 'icon-jiabin', text: '公海资源', mod: 'preseas', power: 'preseas_view', color: '#7E80E8' },
  { icon: 'icon-kehu', text: '我的客户', mod: 'servuser', power: 'servuser_view', color: '#9971E8' },
  { icon: 'icon-lianaiqinggan', text: '约见管理', mod: 'meetorder', power: 'meetorder_view', color: '#7E80E8' },
  { icon: 'icon-fenbushishujuku-', text: '关单库', mod: 'offuser', power: 'offuser_view', color: '#53B996' },
  { icon: 'icon-jiabinku', text: '嘉宾库', mod: 'passuser', power: 'passuser_view', color: '#60CDD4' },
  { icon: 'icon-a-hetong2', text: '我的合同', mod: 'saleorder', power: 'saleorder_view', color: '#56ADE4' },
  { icon: 'icon-shoukuan', text: '我的收款', mod: 'salefinance', power: 'salefinance_view', color: '#EF9E5F' },
  { icon: 'icon-hetong', text: '合同审核', mod: 'order', power: 'order_view', color: '#4ABBE1' },
  { icon: 'icon-shoukuanshenhecaiwu', text: '收款审核', mod: 'finance', power: 'finance_view', color: '#EF9E5F' },
  { icon: 'icon-shanchu1-F', text: '回收站', mod: 'recycle', power: 'recycle_view', color: '#FA6D6D' }
]);

const assistantData = reactive({
  allot_sale_quota: 0,
  sale_new_quota: 0,
  order_audit_quota: 0,
  finance_audit_quota: 0
});

// 徽标配置
const BADGE_CONFIG = {
  待分配: {
    showKey: ['allot', 'allot_view'],
    dataKey: 'allot_sale_quota',
    text: count => count,
    offset: [0, 0]
  },
  我的资源: {
    showKey: ['sale', 'sale_view'],
    dataKey: 'sale_new_quota',
    text: () => '新分',
    offset: [-8, -1]
  },
  合同审核: {
    showKey: ['order', 'order_view'],
    dataKey: 'order_audit_quota',
    text: count => count,
    offset: [0, 0]
  },
  收款审核: {
    showKey: ['finance', 'finance_view'],
    dataKey: 'finance_audit_quota',
    text: count => count,
    offset: [0, 0]
  }
};

// 过滤出有权限的菜单项
const filteredMenu = computed(() => MENU_CONFIG.filter(item => hasPermission(item.mod, item.power)));

const shouldShowBadge = item => {
  const config = BADGE_CONFIG[item.text];
  if (!config) return false;

  const hasPower = hasPermission(...config.showKey);
  return hasPower && assistantData[config.dataKey] > 0;
};

const getBadgeText = item => {
  const config = BADGE_CONFIG[item.text];
  return config?.text(assistantData[config.dataKey]) || '';
};

const getBadgeOffset = item => {
  return BADGE_CONFIG[item.text]?.offset || [0, 0];
};

const navigateToPage = item => {
  if (!item.path) return;

  const tabBarPages = ['/pages/index/index', '/pages/sale/mymember', '/pages/customer/index', '/pages/message/index', '/pages/profile/index'];
  if (tabBarPages.includes(item.path)) {
    uni.switchTab({
      url: item.path
    });
    return;
  }

  uni.navigateTo({
    url: item.path
  });
};

// 获取助手数据
const getAssistantData = async () => {
  const hasRelevantMenu = filteredMenu.value.some(item => Object.keys(BADGE_CONFIG).includes(item.text));
  if (!hasRelevantMenu) return;

  try {
    const res = await initAssistant();
    if (res.ret === 1) {
      const quotaKeys = Object.values(BADGE_CONFIG).map(item => item.dataKey);
      for (const key of quotaKeys) {
        assistantData[key] = Number(res.result.data[key]) || 0;
      }
    }
  } catch (error) {
    console.error(error);
  }
};

onMounted(() => {
  getAssistantData();
});
</script>

<style lang="scss" scoped>
.menu_row {
  @include grid(4, 24rpx, 32rpx);
  background-color: #fff;
  padding: 44rpx 0;
  margin-bottom: 20rpx;

  .menu_item {
    @include flex-column;
    align-items: center;
    box-sizing: border-box;

    .icon_wrapper {
      @include flex-center;
      width: 80rpx;
      height: 80rpx;
      position: relative;
      border-radius: 50%;
      margin-bottom: 16rpx;

      .iconfont {
        font-size: 48rpx;
        color: #fff;
      }
    }

    .menu_text {
      font-size: 24rpx;
      line-height: 1;
      color: #000;
      text-align: center;
    }
  }
}
</style>

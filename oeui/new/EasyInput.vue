<template>
  <view class="search_box" :class="{ disabled: disabled && !readonly }">
    <input
      class="search_easyinput"
      :disabled="disabled"
      :placeholder-style="placeholderStyle"
      :placeholder="placeholder"
      :maxlength="max"
      :type="type"
      v-model="inputValue"
      @input="handleInput"
      @keyup.enter="handleSearch"
    />
    <view class="icon_box">
      <text class="iconfont icon-guanbi" v-show="inputValue" @click="handleClear"></text>
      <text v-if="isSearch" class="iconfont icon-sousuo1" @click="handleSearch"></text>
    </view>
  </view>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  modelValue: String,
  placeholder: {
    type: String,
    default: '请输入搜索内容'
  },
  placeholderStyle: {
    type: String,
    default: 'color: #C0C4CC; font-size:28rpx;'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  isSearch: {
    type: Boolean,
    default: true
  },
  max: {
    type: Number,
    default: undefined
  },
  type: {
    type: String,
    default: 'text'
  },
  readonly: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'input', 'clear', 'search']);
const inputValue = ref(props.modelValue || '');

watch(
  () => props.modelValue,
  newVal => {
    inputValue.value = newVal;
  }
);

const handleInput = event => {
  emit('input', event.detail.value);
  emit('update:modelValue', event.detail.value);
};

const handleClear = () => {
  inputValue.value = '';
  emit('clear');
  emit('update:modelValue', '');
};

const handleSearch = () => {
  if (props.disabled) return;
  const value = inputValue.value.trim();
  if (value) {
    emit('search', value);
  }
};
</script>

<style lang="scss" scoped>
.search_box {
  position: relative;
  display: flex;

  .search_easyinput {
    width: 100%;
    height: 78rpx !important;
    background: #f7f7f9;
    border-radius: 16rpx;
    text-align: left;
    padding: 0 24rpx;
    font-size: 28rpx;
  }

  .icon_box {
    @include flex-center;
    position: absolute;
    right: 24rpx;
    top: 50%;
    transform: translateY(-50%);

    .icon-guanbi {
      font-size: 32rpx;
      color: #c0c4cc;
    }

    .icon-sousuo1 {
      font-size: 44rpx;
      color: #000000;
      margin-left: 32rpx;
    }
  }
}

.search_box.disabled {
  opacity: 0.5;
}
</style>

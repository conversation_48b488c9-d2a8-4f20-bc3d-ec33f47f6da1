<template>
  <PopupContainer ref="popup" :title="title" @confirm="handleConfirm" @close="close">
    <template #content>
      <view class="popup_search">
        <EasyInput v-model="searchKeyword" :placeholder="placeholder" @search="handleSearch" />
      </view>

      <view class="picker_content">
        <view class="option_list">
          <view
            v-for="item in filteredOptions"
            :key="item.value"
            class="option_list_item"
            :class="{
              selected: isSelected(item)
            }"
            @click="handleSelect(item)"
          >
            <text class="item-text">{{ item.text }}</text>
          </view>
        </view>
      </view>

      <view v-if="multiple" class="selected_count">已选择: {{ selectedValues.length }}/{{ maxSelect || '不限' }}</view>
    </template>
  </PopupContainer>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, defineExpose, watch } from 'vue';
import { debounce } from '@/utils/hooks.js';
import PopupContainer from './PopupContainer.vue';
import EasyInput from './EasyInput';

const props = defineProps({
  title: {
    type: String,
    default: '标题'
  },
  options: {
    type: Array,
    default: () => []
  },
  multiple: {
    type: Boolean,
    default: false
  },
  maxSelect: {
    type: Number,
    default: 0
  },
  placeholder: {
    type: String,
    default: '快速搜索'
  },
  modelValue: {
    type: [String, Number, Array],
    default: () => ''
  },
  isMust: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:modelValue', 'confirm', 'change']);

const popup = ref(null);
const searchKeyword = ref('');
const selectedValues = ref([]);

// 将字符串转换为选中项数组
const stringToSelectedItems = str => {
  if (!str) return [];
  const valueArray = str.split(',').filter(v => v !== '');
  return props.options.filter(item => valueArray.includes(String(item.value)));
};

// 将选中项数组转换为字符串
const selectedItemsToString = items => {
  return items.map(item => item.value).join(',');
};

// 初始化选中值
const initSelectedValues = () => {
  if (props.multiple) {
    if (typeof props.modelValue === 'string') {
      return stringToSelectedItems(props.modelValue);
    } else if (Array.isArray(props.modelValue)) {
      return [...props.modelValue];
    }
    return [];
  }
  return props.modelValue || '';
};

watch(
  () => props.modelValue,
  newVal => {
    if (props.multiple) {
      if (typeof newVal === 'string') {
        selectedValues.value = stringToSelectedItems(newVal);
      } else {
        selectedValues.value = Array.isArray(newVal) ? [...newVal] : [];
      }
    } else {
      selectedValues.value = newVal || '';
    }
  },
  { immediate: true }
);

// 搜索功能保持不变
const handleSearch = value => {
  debouncedSearch(value);
};

const filteredOptions = computed(() => {
  const keyword = searchKeyword.value.trim().toLowerCase();
  if (!keyword) return props.options;
  return props.options.filter(item => item.text.toLowerCase().includes(keyword));
});

const debouncedSearch = debounce(value => {
  searchKeyword.value = value;
}, 300);

// 选中判断
const isSelected = item => {
  if (props.multiple) {
    return selectedValues.value.some(v => v.value === item.value);
  }
  return selectedValues.value === item.value;
};

// 选择处理
const handleSelect = item => {
  if (props.multiple) {
    const existsIndex = selectedValues.value.findIndex(v => v.value === item.value);
    if (existsIndex > -1) {
      selectedValues.value.splice(existsIndex, 1);
    } else {
      if (props.maxSelect > 0 && selectedValues.value.length >= props.maxSelect) {
        uni.showToast({
          title: `最多选择${props.maxSelect}项`,
          icon: 'none'
        });
        return;
      }
      selectedValues.value.push(item);
    }
  } else {
    selectedValues.value = selectedValues.value === item.value ? null : item.value;
  }

  emit('change', getSelectedItems());
};

// 获取选中项
const getSelectedItems = () => {
  if (props.multiple) {
    return selectedValues.value.length ? [...selectedValues.value] : [];
  }
  return props.options.find(item => item.value === selectedValues.value) || null;
};

// 确认选择
const handleConfirm = () => {
  if (props.multiple) {
    if (selectedValues.value.length === 0 && props.isMust) {
      uni.showToast({
        title: '请至少选择一项',
        icon: 'none'
      });
      return;
    }

    if (props.maxSelect > 0 && selectedValues.value.length > props.maxSelect) {
      uni.showToast({
        title: `选择数量超过限制`,
        icon: 'none'
      });
      return;
    }

    // 输出字符串格式
    const outputValue = selectedItemsToString(selectedValues.value);
    emit('update:modelValue', outputValue);
    emit('confirm', getSelectedItems());
  } else {
    if (!selectedValues.value && props.isMust) {
      uni.showToast({
        title: '请至少选择一项',
        icon: 'none'
      });
      return;
    }
    emit('update:modelValue', selectedValues.value);
    emit('confirm', getSelectedItems());
  }

  close();
};

const open = () => {
  popup.value?.open('bottom');
  searchKeyword.value = '';
  selectedValues.value = initSelectedValues();
};

const close = () => {
  popup.value?.close();
};

defineExpose({
  open,
  close
});
</script>

<style lang="scss" scoped>
.picker_content {
  margin-top: 28rpx;
}

.selected_count {
  text-align: center;
  color: #666;
  font-size: 24rpx;
}
</style>

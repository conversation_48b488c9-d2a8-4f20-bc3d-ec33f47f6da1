<template>
  <view class="range_option" :class="disabled && 'disabled'">
    <view class="range_option_title" :class="{ value: modelValue }">部门</view>
    <view class="range_option_content" @click="open">
      <text class="range_value" :class="{ value: modelValue }">{{ label }}</text>
      <text class="iconfont icon-youjiantou1"></text>
    </view>
  </view>

  <PopupContainer ref="popup" :title="title" :isDefaultPad="false" :showFooter="!!(shouldShowTree || hasSearchResults)" @confirm="handleConfirm" @close="close">
    <template #content>
      <view class="popup_search">
        <EasyInput v-model="searchKeyword" :placeholder="placeholder" :disabled="!flatNodes.length" @search="handleSearch" />
      </view>

      <scroll-view
        class="tree-container"
        :class="{
          empty: !shouldShowTree && !hasSearchResults
        }"
        scroll-y
      >
        <template v-if="shouldShowTree">
          <TreeNode v-for="item in flatNodes" :key="item.node.roleid" :node="item.node" :level="item.level" @select-node="handleSelect" />
        </template>

        <template v-else-if="hasSearchResults">
          <SearchResults v-for="node in searchResults" :key="node.roleid" :node="node" @select-node="handleSelect" />
        </template>

        <Empty v-else :description="emptyDescription" />
      </scroll-view>
    </template>
  </PopupContainer>
</template>

<script setup>
import { reactive, watch, computed, defineProps, defineEmits, defineExpose, ref } from 'vue';
import PopupContainer from '../PopupContainer';
import EasyInput from '../EasyInput';
import TreeNode from './TreeViewNode';
import SearchResults from '../Tree/SearchResults';
import Empty from '../Empty';

const props = defineProps({
  tree: {
    type: Array,
    default: () => []
  },
  title: {
    type: String,
    default: '标题'
  },
  placeholder: {
    type: String,
    default: '搜索用户昵称/ID'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  modelValue: {
    type: [String, Number],
    default: null
  }
});

const emit = defineEmits(['confirm', 'update:modelValue']);
const popup = ref(null);
const searchKeyword = ref('');
const selectedNode = ref(null);
const tempSelectedNode = ref(null);
const searchResults = ref([]);

const label = computed(() => {
  return selectedNode.value ? selectedNode.value.name || selectedNode.value.rolename : '请选择';
});

const emptyDescription = computed(() => {
  return searchKeyword.value ? '暂无搜索内容' : '暂无内容';
});

const shouldShowTree = computed(() => {
  return !searchKeyword.value && flatNodes.value.length;
});

const hasSearchResults = computed(() => {
  return searchKeyword.value && searchResults.value.length;
});

// 节点状态管理
const nodeStateManager = {
  clearSelection(nodes) {
    const stack = [...nodes, ...searchResults.value];
    while (stack.length) {
      const node = stack.pop();
      node.selected = false;
      if (node?.children) stack.push(...node.children);
    }
  }
};

const processData = nodes => {
  return nodes.map(node => ({
    ...node,
    selected: false,
    children: node.children ? processData(node.children) : []
  }));
};

const normalizedData = reactive({ list: processData(props.tree) });

// 树数据扁平化
const flattenTree = (nodes, level = 0) => {
  const stack = nodes.map(node => ({ node, level }));
  const result = [];

  while (stack.length) {
    const current = stack.shift();
    result.push(current);

    if (current.node.children?.length) {
      const children = current.node.children.map(child => ({
        node: child,
        level: current.level + 1
      }));
      stack.splice(0, 0, ...children);
    }
  }

  return result;
};

const flatNodes = computed(() => flattenTree(normalizedData.list));

// 选中节点
const handleSelect = node => {
  nodeStateManager.clearSelection([...normalizedData.list, ...searchResults.value]);

  const target = node.origin || node;
  target.selected = true;
  tempSelectedNode.value = target;
};

// 确认选中节点
const handleConfirm = () => {
  if (!tempSelectedNode.value) {
    uni.showToast({ title: '至少选择一项', icon: 'none' });
    return;
  }

  selectedNode.value = tempSelectedNode.value;
  emit('update:modelValue', selectedNode.value.roleid);
  emit('confirm', selectedNode.value);
  close();
};

const handleSearch = () => {
  const keyword = searchKeyword.value.trim();
  if (!keyword) {
    searchResults.value = [];
    return;
  }

  const keywordLower = keyword.toLowerCase();
  const searchList = [];
  const stack = normalizedData.list.map(node => ({
    node,
    parent: null,
    origin: node
  }));

  while (stack.length) {
    const { node, parent } = stack.pop();
    if (node.name?.toLowerCase().includes(keywordLower) || node.rolename?.toLowerCase().includes(keywordLower)) {
      searchList.push({
        roleid: node.roleid,
        name: node.rolename,
        parentRolename: parent?.rolename || null,
        selected: false
      });
    }

    if (node.children?.length) {
      stack.push(...node.children.map(child => ({ node: child, parent: node })));
    }
  }

  searchResults.value = searchList;
};

// 根据id匹配
const findNodeById = (nodes, targetId) => {
  if (!targetId) return null;
  const stack = [...nodes];
  while (stack.length) {
    const node = stack.pop();
    if (node.roleid === targetId) return node;
    if (node.children?.length) stack.push(...node.children);
  }
  return null;
};

const open = () => {
  if (props.disabled) return;
  if (selectedNode.value) {
    const node = findNodeById(normalizedData.list, selectedNode.value.roleid);
    if (node) {
      tempSelectedNode.value = node;
      node.selected = true;
    }
  }
  popup.value?.open('bottom');
  searchKeyword.value = '';
};

const close = () => {
  if (tempSelectedNode.value) {
    tempSelectedNode.value.selected = false;
  }
  tempSelectedNode.value = null;
  popup.value?.close();
};

watch(
  () => props.modelValue,
  newVal => {
    if (!newVal) {
      selectedNode.value = null;
      return;
    }

    const node = findNodeById(normalizedData.list, newVal);
    if (node) {
      selectedNode.value = node;
    }
  },
  { immediate: true, deep: true }
);

watch(
  () => props.tree,
  newVal => {
    normalizedData.list = processData(newVal);
    searchResults.value = [];
  },
  { immediate: true, deep: true }
);

watch(
  () => searchKeyword.value,
  newVal => {
    if (!newVal.trim()) {
      searchResults.value = [];
      return;
    }

    handleSearch();
  },
  { deep: true }
);

defineExpose({ open, close });
</script>

<style lang="scss" scoped>
.tree-container {
  min-height: 700rpx;
  max-height: 700rpx;
}

.tree-container.empty {
  @include flex-center;
  height: 100%;
}

.popup_search {
  margin-bottom: 10rpx;
  padding: 0 32rpx;
}

.range_option {
  @include flex-between;
  height: 84rpx;
  margin-bottom: 8rpx;
  background-color: #fff;

  &.disabled {
    opacity: 0.5;
  }

  &_title {
    width: 160rpx;
    font-size: 32rpx;
    color: #000;
  }

  &_content {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 1;
    height: 100%;

    text.value {
      color: #373d49;
    }

    .range_value {
      color: #c0c4cc;
      margin-right: 12rpx;
      font-size: 32rpx;
    }
  }
}
</style>

import http from '@/utils/api.js';
import dayjs from 'dayjs';
const MODULE = 'vuewap';

const formatValue = (row, column) => {
  // 格式化函数，第一个参数是行数据，第二个参数是列配置对象
  if (row[column.property] == null || row[column.property] === '') {
    // 判断值是否为空
    return '--'; // 如果为空，返回占位文本
  }
  return row[column.property]; // 如果不为空，返回原值
};

const ContentScrollTop = () => {
  let view = document.querySelector('.content_view');
  view && view.scrollTo(0, 0); //初始化滚动条
};

const setHtmlExp = result => {
  if (result) {
    let exp = new RegExp('&amp;nbsp;', 'g');
    result = result
      .replace(result ? /&(?!#?\w+;)/g : /&/g, '&amp;')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&rarr;/g, '-')
      .replace(/&ldquo;/g, '"')
      .replace(/&rdquo;/g, '"');
    return result.replace(exp, '\u3000');
  }
};

const toImgUrl = (key, w = '', h = '') => {
  const imgUrl = import.meta.env.VITE_STATIC_URL + 'statics/';
  let url = imgUrl + key + '.oesc';
  if (w || h) {
    url += `?w_h=${w},${h}`;
  }
  return url;
};

const getTime = (nS = 0, isS = false, type = 'num', connector = '-') => {
  if (nS == 0) {
    return '--';
  }
  let time = new Date(parseInt(nS) * 1000);
  let month = time.getMonth() + 1;
  if (month.toString().length == 1) {
    month = '0' + month;
  }
  let date = time.getDate();
  if (date.toString().length == 1) {
    date = '0' + date;
  }

  let hours = time.getHours();
  let minutes = time.getMinutes();
  if (hours.toString().length == 1) {
    hours = '0' + hours;
  }
  if (minutes.toString().length == 1) {
    minutes = '0' + minutes;
  }
  let dateText = '';
  if (type == 'text') {
    // 为了比较日期，我们需要设置时间部分为0，只比较年月日
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    time.setHours(0, 0, 0, 0);
    const diffInDays = (time - today) / (1000 * 60 * 60 * 24);
    if (diffInDays == 0) {
      dateText = '今天';
    } else if (diffInDays === 1) {
      dateText = '明天';
    } else {
      // if(time.getFullYear() == new Date().getFullYear()){
      //   dateText =  month + '-' + date
      // }else{
      //   dateText = time.getFullYear()+'-'+ month + '-' + date
      // }
      dateText = time.getFullYear() + connector + month + connector + date;
    }

    dateText += ' ' + hours + ':' + minutes;
  } else {
    dateText = time.getFullYear() + connector + month + connector + date + ' ' + hours + ':' + minutes;
  }

  if (isS) {
    let s = ('0' + time.getSeconds()).slice(-2);

    dateText += ':' + s;
  }
  return dateText;
};
const getDate = (nS = 0, type = 's') => {
  if (nS == 0) {
    return '--';
  }

  let time = 0;
  if (type == 's') {
    time = new Date(parseInt(nS) * 1000);
  } else {
    time = new Date(parseInt(nS));
  }

  let month = time.getMonth() + 1;
  if (month.toString().length == 1) {
    month = '0' + month;
  }
  let date = time.getDate();
  if (date.toString().length == 1) {
    date = '0' + date;
  }

  let dateText = time.getFullYear() + '-' + month + '-' + date;
  return dateText;
};

const getTimeText = (nS = 0, isNoTime = true) => {
  let time = new Date(parseInt(nS) * 1000);
  let month = time.getMonth() + 1;
  if (month.toString().length == 1) {
    month = '0' + month;
  }
  let date = time.getDate();
  if (date.toString().length == 1) {
    date = '0' + date;
  }
  let dateText = time.getFullYear() + ' 年 ' + month + ' 月 ' + date + ' 日';
  if (!isNoTime) {
    let hours = time.getHours();
    let minutes = time.getMinutes();
    if (hours.toString().length == 1) {
      hours = '0' + hours;
    }
    if (minutes.toString().length == 1) {
      minutes = '0' + minutes;
    }
    dateText += ' ' + hours + ':' + minutes;
  }
  return dateText;
};

const getWeekText = (nS = 0) => {
  let dateTime = new Date(parseInt(nS) * 1000);
  const days = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];

  return days[dateTime.getDay()];
};

const getPickerTxt = (val, type, cond = false) => {
  let picker = uni.getStorageSync(type);
  let text = '';
  if (picker) {
    picker = JSON.parse(picker);
    for (let i = 0; i < picker.length; i++) {
      if (picker[i].value == val) {
        text = picker[i].text;
      }
    }
  }
  if (!cond) {
    //return text || val || '--'
    return text || '--';
  } else {
    //return text || val || '不限'
    return text || '不限';
  }
};
//根据秒数返回HH:MM:SS格式
function formatSeconds(seconds) {
  let hours = Math.floor(seconds / 3600);
  let minutes = Math.floor((seconds % 3600) / 60);
  let secs = seconds % 60;

  hours = hours < 10 ? '0' + hours : hours;
  minutes = minutes < 10 ? '0' + minutes : minutes;
  secs = secs < 10 ? '0' + secs : secs;

  return hours + ':' + minutes + ':' + secs;
}

//根据秒数返回天、时、分、秒格式
function formatSeconds_t(seconds, isDetail = true, isSecs = false) {
  let day = Math.floor(seconds / (3600 * 24));
  let hours = Math.floor((seconds % (3600 * 24)) / 3600);
  let minutes = Math.floor((seconds % 3600) / 60);
  let secs = seconds % 60;

  hours = hours < 10 ? '0' + hours : hours;
  minutes = minutes < 10 ? '0' + minutes : minutes;
  secs = secs < 10 ? '0' + secs : secs;
  if (!isDetail) {
    return day + '天';
  }
  if (day > 0) {
    if (isSecs) {
      return day + '天' + hours + '时' + minutes + '分' + secs + '秒';
    } else {
      return day + '天' + hours + '时' + minutes + '分';
    }
  } else {
    if (isSecs) {
      return hours + '时' + minutes + '分' + secs + '秒';
    } else {
      return hours + '时' + minutes + '分';
    }
  }
}

//返回标签富文本
const getEmf = val => {
  var regexp = new RegExp('{emf_([0-9]+)}', 'ig');
  var text = val.replace(regexp, function (str, $1) {
    return '<img class="img_20" src="' + import.meta.env.VITE_STATIC_URL + 'tpl/static/images/face/' + $1 + '.gif">';
  });
  return text;
};

const getPickerDistTxt = (val, type) => {
  let picker = uni.getStorageSync(type);
  let text = '';
  let step = 0;
  if (picker) gettxt(JSON.parse(picker));
  function gettxt(arr) {
    for (let q = 0; q < arr.length; q++) {
      if (arr[q].value == val[step]) {
        if (!text) {
          text = arr[q].text;
        } else {
          text += '-' + arr[q].text;
        }

        if (arr[q].children) {
          step++;
          gettxt(arr[q].children);
        }
      }
    }
  }

  return text || '--';
};

//配置富文本

//水印
function weaterMaskFn(params = {}) {
  if (document.getElementById('watermark_index') || !document.querySelector('.content_view')) {
    return;
  }
  let waterMarkDiv = document.createElement('div');
  waterMarkDiv.id = 'watermark_index';
  document.querySelector('.content_view').appendChild(waterMarkDiv);
  let wrapNum = 10, // 15个换行。
    top = -30, // 距离顶部距离。
    text = params.text; // 水印文案。

  // 修改left、top距离，实现满屏水印效果。
  let wrapFn = (i, type) => {
    let multiple = Math.floor(i / wrapNum);
    if (type == 'left') {
      let temp = i * 240 + 'px';
      temp = (i - multiple * wrapNum) * 240 + 'px';
      return temp;
    }
    if (type == 'top') {
      let temp = top + 'px';
      temp = top + (params.marginTop || 150) * multiple + 'px';
      return temp;
    }
  };
  // 循环，创建多个水印。
  for (var i = 0; i < 100; i++) {
    let span = document.createElement('span');
    span.style.position = 'fixed';
    span.style.zIndex = '99999';
    span.style.whiteSpace = 'nowrap'; // 禁止文字换行
    span.style.padding = '100px';
    span.style.transform = `rotate(${params.rotate || '-30'}deg)`; // 旋转-30deg。
    span.style.fontSize = params.fontSize || '14px';
    span.style.pointerEvents = 'none'; // css事件穿透
    span.style.color = params.color || '#0f0';
    span.style.left = wrapFn(i, 'left');
    span.style.top = wrapFn(i, 'top');
    span.style.opacity = params.opacity || 0.5;
    span.innerText = text;
    waterMarkDiv?.appendChild(span);
  }
}

const formatDate = date => {
  return dayjs(date).format('YYYY-MM-DD');
};

export {
  formatValue,
  ContentScrollTop,
  setHtmlExp,
  toImgUrl,
  getTime,
  getDate,
  getTimeText,
  getWeekText,
  getPickerTxt,
  getPickerDistTxt,
  formatSeconds,
  formatSeconds_t,
  getEmf,
  weaterMaskFn,
  formatDate
};

<template>
  <uni-drawer ref="drawer" mode="right" :mask="false" :mask-click="false">
    <view class="member_content">
      <CustomNavBar title="编辑资源" :isBack="false" showLeft @back="back" />
      <scroll-view style="height: calc(100vh - 92rpx)" class="scroll-view-box" scroll-y>
        <view class="form_box" style="border-bottom: 16rpx solid #f7f7f9">
          <view class="form_item">
            <text class="module_title">基础资料</text>
          </view>
          <view class="form_item avatar">
            <view class="row_title">上传头像</view>
            <view class="row_value">
              <view class="avatar_seat">
                <image v-if="stateString.headimg" :src="stateString.headimg_url" @click="handleUploadAvatar"></image>
                <image v-else @click="handleUploadAvatar" :src="avatar_d"></image>
              </view>
              <view class="iconfont icon-youjiantou1"></view>
            </view>
          </view>

          <view class="form_item">
            <view class="row_title">性别</view>
            <GroupButton v-model="state.gender" is-gap :options="filteredGenderOptions" />
          </view>

          <view class="form_item">
            <view class="row_title">为谁征婚</view>
            <view class="row_value" @click="showPickerView('选择为谁征婚', 'maytype', 'maytype')">
              <span v-if="state.maytype" class="picker_text">{{ getOptionText('maytype', state.maytype) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">手机号</view>
            <view class="row_value">
              <InputForm v-model="stateString.mobile" type="text" :maxlength="11" disabled :showClear="false" placeholder="请输入" align="right" />
            </view>
          </view>

          <view class="form_item">
            <view class="row_title required">昵称</view>
            <view class="row_value">
              <InputForm v-model="stateString.nickname" :maxlength="20" placeholder="请输入" align="right" />
            </view>
          </view>

          <view class="form_item">
            <view class="row_title">资源来源</view>
            <view class="row_value" @click="showPickerView('选择资源来源', 'crm_fromcat', 'fromcat')">
              <span v-if="state.fromcat" class="picker_text">{{ getOptionText('crm_fromcat', state.fromcat) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">年龄</view>
            <view class="row_value" @click="showDatePicker">
              <span v-if="formatAgeText" class="picker_text">{{ formatAgeText }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">生肖</view>
            <view class="row_value" @click="showPickerView('选择生肖', 'lunar', 'lunar')">
              <span v-if="state.lunar" class="picker_text">{{ getOptionText('lunar', state.lunar) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">星座</view>
            <view class="row_value" @click="showPickerView('选择星座', 'astro', 'astro')">
              <span v-if="state.astro" class="picker_text">{{ getOptionText('astro', state.astro) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item form_button_item">
            <view class="row_title" style="margin-bottom: 20rpx">婚况</view>
            <ButtonSelector v-model="state.marry" :options="picker.marry" />
          </view>

          <view class="form_item">
            <view class="row_title">{{ config.dist_title }}</view>
            <view class="row_value" @click="showArea('选择居住地', 'district')">
              <span v-if="state.dist1" class="picker_text">
                {{ getPickerDistTxt([state.dist1, state.dist2, state.dist3, state.dist4], 'district') }}
              </span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">{{ config.home_title }}</view>
            <view class="row_value" @click="showArea('选择户籍地', 'hometown')">
              <span v-if="state.home1" class="picker_text">
                {{ getPickerDistTxt([state.home1, state.home2, state.home3, state.home4], 'hometown') }}
              </span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">身高</view>
            <view class="row_value" @click="showPickerView('选择身高', 'height', 'height')">
              <span v-if="state.height" class="picker_text">{{ getOptionText('height', state.height) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">体重</view>
            <view class="row_value" @click="showPickerView('选择体重', 'weight', 'weight')">
              <span v-if="state.weight" class="picker_text">{{ getOptionText('weight', state.weight) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item form_button_item">
            <view class="row_title" style="margin-bottom: 20rpx">学历</view>
            <ButtonSelector v-model="state.education" :options="picker.education" />
          </view>

          <view class="form_item">
            <view class="row_title">毕业院校</view>
            <view class="row_value">
              <InputForm v-model="stateString.school" :maxlength="20" placeholder="请输入" align="right" />
            </view>
          </view>

          <view class="form_item">
            <view class="row_title">职业</view>
            <view class="row_value" @click="showPickerView('选择职业', 'job', 'job')">
              <span v-if="state.job" class="picker_text">{{ getOptionText('job', state.job) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item form_button_item">
            <view class="row_title" style="margin-bottom: 20rpx">年收入</view>
            <ButtonSelector v-model="state.salary" :options="picker.salary" />
          </view>

          <view class="form_item form_button_item">
            <view class="row_title" style="margin-bottom: 20rpx">资源状态</view>
            <ButtonSelector v-model="state.flag" :options="picker.flag" />
          </view>

          <view class="form_item">
            <view class="row_title">服务等级</view>
            <view class="row_value" @click="showPickerView('选择服务等级', 'crm_servlevel', 'servlevel')">
              <span v-if="state.job" class="picker_text">{{ getOptionText('crm_servlevel', state.servlevel) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">微信号</view>
            <view class="row_value">
              <!--  @blur="handleVerifyWeixin" -->
              <InputForm v-model="stateString.weixin" :maxlength="30" @input="handleInputWeixin" @clear="handleClearWeixin" placeholder="请输入" align="right" />
            </view>
          </view>
          <view v-if="showWeixintips" class="mobile_error mb8">{{ wxmsg }}</view>

          <view class="form_item">
            <view class="row_title" style="width: 205rpx">其他联系方式</view>
            <view class="row_value">
              <InputForm v-model="stateString.othermobi" :maxlength="30" placeholder="请输入" align="right" />
            </view>
          </view>

          <view class="form_button_item">
            <view class="row_title" style="width: 400rpx; margin-bottom: 24rpx">备注信息(仅后台显示)</view>
            <EasyTextarea v-model="stateString.remark" placeholder="请输入" :max="2000" :autoHeight="false" />
          </view>
        </view>

        <view class="form_box" style="border-bottom: 16rpx solid #f7f7f9">
          <view class="form_item mb16i">
            <text class="module_title">择偶要求</text>
          </view>

          <view class="form_item">
            <view class="row_title">{{ config.dist_title }}</view>
            <view class="row_value" @click="showArea('选择居住地', 'cond_district')">
              <span v-if="stateCond.area1" class="picker_text">
                {{ getPickerDistTxt([stateCond.area1, stateCond.area2, stateCond.area3], 'district') }}
              </span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">年龄区间</view>
            <view class="row_value" @click="showPickerView2('选择年龄区间', 'condAges', 'condAges')">
              <span v-if="stateCond.age1 && stateCond.age2" class="picker_text">{{ stateCond.age1 }}岁 - {{ stateCond.age2 }}岁</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">身高区间</view>
            <view class="row_value" @click="showPickerView2('选择身高区间', 'condHeights', 'height')">
              <span v-if="stateCond.height1 && stateCond.height2" class="picker_text">{{ stateCond.height1 }}cm - {{ stateCond.height2 }}cm</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">体重区间</view>
            <view class="row_value" @click="showPickerView2('选择体重区间', 'condWeights', 'weight')">
              <span v-if="stateCond.weight1 && stateCond.weight2" class="picker_text">{{ stateCond.weight1 }}kg - {{ stateCond.weight2 }}kg</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item form_button_item">
            <view class="row_title" style="margin-bottom: 20rpx">婚况</view>
            <ButtonSelector v-model="stateCondMore.cond_marry" multiple :options="picker.marry" />
          </view>

          <view class="form_item">
            <view class="row_title">学历</view>
            <view class="row_value" @click="showPicker('选择学历', 'cond_education', 'cond_education')">
              <span v-if="stateCondMore.cond_education" class="picker_text ws">
                {{ formatSelectedText(stateCondMore.cond_education, getOptionsByKey('cond_education')) }}
              </span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">年收入</view>
            <view class="row_value" @click="showPicker('选择年收入', 'cond_salary', 'cond_salary')">
              <span v-if="stateCondMore.cond_salary" class="picker_text ws">{{ formatSelectedText(stateCondMore.cond_salary, getOptionsByKey('cond_salary')) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">购房</view>
            <view class="row_value" @click="showPicker('选择购房', 'cond_house', 'cond_house')">
              <span v-if="stateCondMore.cond_house" class="picker_text ws">{{ formatSelectedText(stateCondMore.cond_house, getOptionsByKey('cond_house')) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item form_button_item">
            <view class="row_title" style="margin-bottom: 20rpx">购车</view>
            <ButtonSelector v-model="stateCondMore.cond_car" multiple :options="picker.car" />
          </view>

          <view class="form_item">
            <view class="row_title">小孩情况</view>
            <view class="row_value" @click="showPicker('选择小孩情况', 'cond_child', 'cond_child')">
              <span v-if="stateCondMore.cond_child" class="picker_text ws">{{ formatSelectedText(stateCondMore.cond_child, getOptionsByKey('cond_child')) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">生肖</view>
            <view class="row_value" @click="showPicker('选择生肖', 'cond_lunar', 'cond_lunar')">
              <span v-if="stateCondMore.cond_lunar" class="picker_text ws">{{ formatSelectedText(stateCondMore.cond_lunar, getOptionsByKey('cond_lunar')) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">星座</view>
            <view class="row_value" @click="showPicker('选择星座', 'cond_astro', 'cond_astro')">
              <span v-if="stateCondMore.cond_astro" class="picker_text ws">{{ formatSelectedText(stateCondMore.cond_astro, getOptionsByKey('cond_astro')) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_button_item">
            <view class="row_title" style="margin-bottom: 24rpx">其他要求</view>
            <EasyTextarea v-model="stateCond.other" placeholder="请输入" :max="1000" :autoHeight="false" />
          </view>
        </view>

        <view class="form_box" style="border-bottom: 16rpx solid #f7f7f9">
          <view class="form_item">
            <text class="module_title">详细信息</text>
          </view>

          <view class="form_item">
            <view class="row_title">民族</view>
            <view class="row_value" @click="showPickerView('选择民族', 'national', 'national')">
              <span v-if="state.national" class="picker_text">{{ getOptionText('national', state.national) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">信仰</view>
            <view class="row_value" @click="showPickerView('选择信仰', 'faith', 'faith')">
              <span v-if="state.faith" class="picker_text">{{ getOptionText('faith', state.faith) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">血型</view>
            <view class="row_value" @click="showPickerView('选择血型', 'blood', 'blood')">
              <span v-if="state.blood" class="picker_text">{{ getOptionText('blood', state.blood) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item form_button_item">
            <view class="row_title" style="margin-bottom: 20rpx">交友类型</view>
            <ButtonSelector v-model="state.sort" :options="picker.sort" />
          </view>

          <view class="form_item">
            <view class="row_title">小孩情况</view>
            <view class="row_value" @click="showPickerView('选择小孩情况', 'child', 'child')">
              <span v-if="state.child > 0" class="picker_text">{{ getOptionText('child', state.child) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item form_button_item">
            <view class="row_title" style="margin-bottom: 20rpx">身体状况</view>
            <ButtonSelector v-model="state.health" :options="picker.health" />
          </view>

          <view class="form_item">
            <view class="row_title">购房情况</view>
            <view class="row_value" @click="showPickerView('选择购房情况', 'house', 'house')">
              <span v-if="state.house" class="picker_text">{{ getOptionText('house', state.house) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">购车情况</view>
            <view class="row_value" @click="showPickerView('选择购车情况', 'car', 'car')">
              <span v-if="state.car" class="picker_text">{{ getOptionText('car', state.car) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">是否吸烟</view>
            <view class="row_value" @click="showPickerView('选择吸烟情况', 'smoking', 'smoking')">
              <span v-if="state.smoking" class="picker_text">{{ getOptionText('smoking', state.smoking) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">是否喝酒</view>
            <view class="row_value" @click="showPickerView('选择喝酒情况', 'drinking', 'drinking')">
              <span v-if="state.drinking" class="picker_text">{{ getOptionText('drinking', state.drinking) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title" style="width: 220rpx">公司/单位名称</view>
            <view class="row_value">
              <InputForm v-model="stateString.company" :maxlength="30" placeholder="请输入" align="right" />
            </view>
          </view>

          <view class="form_item">
            <view class="row_title" style="width: 220rpx">公司/单位类型</view>
            <view class="row_value" @click="showPickerView('选择公司/单位类型', 'companytype', 'companytype')">
              <span v-if="state.companytype" class="picker_text">{{ getOptionText('companytype', state.companytype) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">父母情况</view>
            <view class="row_value" @click="showPickerView('选择父母情况', 'parentstatus', 'parentstatus')">
              <span v-if="state.parentstatus > 0" class="picker_text">{{ getOptionText('parentstatus', state.parentstatus) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">父母经济</view>
            <view class="row_value" @click="showPickerView('选择父母经济', 'parenteconomic', 'parenteconomic')">
              <span v-if="state.parenteconomic > 0" class="picker_text">{{ getOptionText('parenteconomic', state.parenteconomic) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">父母医保</view>
            <view class="row_value" @click="showPickerView('选择父母医保', 'parentshealthcare', 'parentshealthcare')">
              <span v-if="state.parentshealthcare > 0" class="picker_text">{{ getOptionText('parentshealthcare', state.parentshealthcare) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">父亲工作</view>
            <view class="row_value" @click="showPickerView('选择父亲工作', 'fatherwork', 'fatherwork')">
              <span v-if="state.fatherwork > 0" class="picker_text">{{ getOptionText('fatherwork', state.fatherwork) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">母亲工作</view>
            <view class="row_value" @click="showPickerView('选择母亲工作', 'motherwork', 'motherwork')">
              <span v-if="state.motherwork > 0" class="picker_text">{{ getOptionText('motherwork', state.motherwork) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">家中排行</view>
            <view class="row_value" @click="showPickerView('选择家中排行', 'tophome', 'tophome')">
              <span v-if="state.tophome > 0" class="picker_text">{{ getOptionText('tophome', state.tophome) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item form_button_item">
            <view class="row_title" style="margin-bottom: 20rpx; width: 230rpx">是否接受异地恋</view>
            <ButtonSelector v-model="state.allopatry" :options="picker.allopatry" />
          </view>

          <view class="form_item">
            <view class="row_title" style="width: 210rpx">希望对方看重</view>
            <view class="row_value" @click="showPickerView('选择希望对方看重', 'focus', 'focus')">
              <span v-if="state.focus > 0" class="picker_text">{{ getOptionText('focus', state.focus) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title" style="width: 230rpx">期待的婚礼形式</view>
            <view class="row_value" @click="showPickerView('选择期待的婚礼形式', 'wedding', 'wedding')">
              <span v-if="state.wedding > 0" class="picker_text">{{ getOptionText('wedding', state.wedding) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title" style="width: 230rpx">厨艺状况</view>
            <view class="row_value" @click="showPickerView('选择厨艺状况', 'cooking', 'cooking')">
              <span v-if="state.cooking > 0" class="picker_text">{{ getOptionText('cooking', state.cooking) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title" style="width: 230rpx">家务分工</view>
            <view class="row_value" @click="showPickerView('选择家务分工', 'housework', 'housework')">
              <span v-if="state.housework > 0" class="picker_text">{{ getOptionText('housework', state.housework) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item form_button_item">
            <view class="row_title" style="margin-bottom: 20rpx; width: 270rpx">对方父母同住</view>
            <ButtonSelector v-model="state.liveparent" :options="picker.liveparent" />
          </view>

          <view class="form_item">
            <view class="row_title" style="width: 230rpx">作息习惯</view>
            <view class="row_value" @click="showPickerView('选择作息习惯', 'rest', 'rest')">
              <span v-if="state.rest > 0" class="picker_text">{{ getOptionText('rest', state.rest) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item form_button_item">
            <view class="row_title" style="margin-bottom: 20rpx; width: 270rpx">是否要小孩</view>
            <ButtonSelector v-model="state.wantchild" :options="picker.wantchild" />
          </view>

          <view class="form_item">
            <view class="row_title">兴趣爱好</view>
            <view class="row_value" @click="showPicker('选择兴趣爱好', 'interest', 'interest')">
              <span v-if="stateCondMore.interest" class="picker_text ws">{{ formatSelectedText(stateCondMore.interest, getOptionsByKey('interest')) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>

          <view class="form_item">
            <view class="row_title">掌握语言</view>
            <view class="row_value" @click="showPicker('选择掌握语言', 'language', 'language')">
              <span v-if="stateCondMore.language" class="picker_text ws">{{ formatSelectedText(stateCondMore.language, getOptionsByKey('language')) }}</span>
              <span v-else class="picker_no">请选择</span>
            </view>
            <view class="iconfont icon-youjiantou1"></view>
          </view>
        </view>

        <template v-if="userphoto.length">
          <view class="form_box" style="border-bottom: 16rpx solid #f7f7f9">
            <view class="form_item mb16i">
              <text class="module_title">客户相册(此相册为线上相册)</text>
            </view>

            <view class="upload_container">
              <view class="upload_annex">
                <view class="upload_box" v-for="(item, index) in userphoto" :key="item.attid" @click="previewImage(userphoto, index)">
                  <image :src="item.drawimg_url" mode="aspectFill" />
                </view>
              </view>
            </view>
          </view>
        </template>

        <view class="form_box" style="border-bottom: 16rpx solid #f7f7f9">
          <view class="form_item mb16i">
            <text class="module_title">简介</text>
          </view>

          <view class="form_button_item" style="margin-bottom: 32rpx !important">
            <view class="row_title" style="margin-bottom: 24rpx">自我介绍</view>
            <EasyTextarea v-model="stateString.intro" placeholder="请输入" :max="1000" :autoHeight="false" />
          </view>
        </view>

        <view class="form_box" style="border-bottom: 16rpx solid #f7f7f9">
          <view class="form_item mb16i">
            <text class="module_title">客户资料附件(仅CRM端显示)</text>
          </view>

          <view class="upload_container">
            <view class="upload_annex">
              <view class="upload_box flex flex_dc" @click="handleUploadPhoto">
                <text class="iconfont icon-jiahao"></text>
              </view>
              <view class="upload_box" v-for="(item, index) in photoList" :key="item.attid" @click="previewImage(photoList, index)">
                <view class="delete_icon flex flex_dc" @click.stop="handleDeletePhoto(index, item)">
                  <text class="iconfont icon-guanbi2"></text>
                </view>
                <image :src="item.drawimg_url" mode="aspectFill" />
              </view>
            </view>
          </view>
        </view>

        <view class="form_box" style="border-bottom: 16rpx solid #f7f7f9">
          <view class="form_item mb16i">
            <text class="module_title">认证</text>
          </view>

          <view class="rz_item">
            <view class="rz_title">实名认证</view>
            <view class="id_rz_row flex flex_ac flex_jsb mb12">
              <view class="id_upload flex flex_dc flex_v" @click="handleUploadIdCard('paper1')">
                <view v-if="stateString.paper1" class="delete_icon flex flex_dc" @click.stop="handleDeleteRz('paper1')">
                  <text class="iconfont icon-guanbi2"></text>
                </view>
                <template v-if="!stateString.paper1">
                  <view class="upload_file">点击上传</view>
                  <view class="upload_tips">身份证头像面</view>
                </template>
                <image v-else :src="stateString.paper1_url" mode="aspectFill" />
              </view>
              <view class="id_upload flex flex_dc flex_v" @click="handleUploadIdCard('paper2')">
                <view v-if="stateString.paper2" class="delete_icon flex flex_dc" @click.stop="handleDeleteRz('paper2')">
                  <text class="iconfont icon-guanbi2"></text>
                </view>
                <template v-if="!stateString.paper2">
                  <view class="upload_file">点击上传</view>
                  <view class="upload_tips">身份证国徽面</view>
                </template>
                <image v-else :src="stateString.paper2_url" mode="aspectFill" />
              </view>
              <view class="id_upload flex flex_dc flex_v" @click="handleUploadIdCard('paper3')">
                <view v-if="stateString.paper3" class="delete_icon flex flex_dc" @click.stop="handleDeleteRz('paper3')">
                  <text class="iconfont icon-guanbi2"></text>
                </view>
                <template v-if="!stateString.paper3">
                  <view class="upload_file">点击上传</view>
                  <view class="upload_tips">手持身份证</view>
                </template>
                <image v-else :src="stateString.paper3_url" mode="aspectFill" />
              </view>
            </view>
            <view class="form_item">
              <view class="row_title">姓名</view>
              <view class="row_value">
                <InputForm v-model="stateString.truename" :maxlength="20" placeholder="请输入" align="right" />
              </view>
            </view>
            <view class="form_item">
              <view class="row_title">身份证</view>
              <view class="row_value">
                <InputForm v-model="stateString.idnumber" :maxlength="18" placeholder="请输入" align="right" />
              </view>
            </view>
            <view class="form_item">
              <view class="row_title flex flex_ac" style="width: 260rpx">
                <text>认证状态</text>
                <text :style="{ color: state.idrz == 1 ? '#6365E0' : '#8898B6' }">
                  {{ state.idrz == 1 ? '(已认证)' : '(未认证)' }}
                </text>
              </view>
              <view class="row_value">
                <switch color="#6365E0" :checked="state.idrz == 1" data-name="idrz" @change="handleSwitchChange" />
              </view>
            </view>
          </view>
          <view class="rz_item">
            <view class="rz_title">房产认证</view>
            <view class="id_rz_row flex flex_ac flex_jsb mb12">
              <view class="id_upload flex flex_dc flex_v" @click="handleUploadIdCard('houseimg')">
                <view v-if="stateString.houseimg" class="delete_icon flex flex_dc" @click.stop="handleDeleteRz('houseimg')">
                  <text class="iconfont icon-guanbi2"></text>
                </view>
                <template v-if="!stateString.houseimg">
                  <view class="upload_file">点击上传</view>
                  <view class="upload_tips">房产证件</view>
                </template>
                <image v-else :src="stateString.houseimg_url" mode="aspectFill" />
              </view>
            </view>
            <view class="form_item">
              <view class="row_title flex flex_ac" style="width: 260rpx">
                <text>认证状态</text>
                <text :style="{ color: state.houserz == 1 ? '#6365E0' : '#8898B6' }">
                  {{ state.houserz == 1 ? '(已认证)' : '(未认证)' }}
                </text>
              </view>
              <view class="row_value">
                <switch color="#6365E0" :checked="state.houserz == 1" data-name="houserz" @change="handleSwitchChange" />
              </view>
            </view>
          </view>
          <view class="rz_item">
            <view class="rz_title">车子认证</view>
            <view class="id_rz_row flex flex_ac flex_jsb mb12">
              <view class="id_upload flex flex_dc flex_v" @click="handleUploadIdCard('carimg')">
                <view v-if="stateString.carimg" class="delete_icon flex flex_dc" @click.stop="handleDeleteRz('carimg')">
                  <text class="iconfont icon-guanbi2"></text>
                </view>
                <template v-if="!stateString.carimg">
                  <view class="upload_file">点击上传</view>
                  <view class="upload_tips">车子证件</view>
                </template>
                <image v-else :src="stateString.carimg_url" mode="aspectFill" />
              </view>
            </view>
            <view class="form_item">
              <view class="row_title flex flex_ac" style="width: 260rpx">
                <text>认证状态</text>
                <text :style="{ color: state.carrz == 1 ? '#6365E0' : '#8898B6' }">
                  {{ state.carrz == 1 ? '(已认证)' : '(未认证)' }}
                </text>
              </view>
              <view class="row_value">
                <switch color="#6365E0" :checked="state.carrz == 1" data-name="carrz" @change="handleSwitchChange" />
              </view>
            </view>
          </view>
          <view class="rz_item">
            <view class="rz_title">学历认证</view>
            <view class="id_rz_row flex flex_ac flex_jsb mb12">
              <view class="id_upload flex flex_dc flex_v" @click="handleUploadIdCard('eduimg')">
                <view v-if="stateString.eduimg" class="delete_icon flex flex_dc" @click.stop="handleDeleteRz('eduimg')">
                  <text class="iconfont icon-guanbi2"></text>
                </view>
                <template v-if="!stateString.eduimg">
                  <view class="upload_file">点击上传</view>
                  <view class="upload_tips">学历证件</view>
                </template>
                <image v-else :src="stateString.eduimg_url" mode="aspectFill" />
              </view>
            </view>
            <view class="form_item">
              <view class="row_title flex flex_ac" style="width: 260rpx">
                <text>认证状态</text>
                <text :style="{ color: state.edurz == 1 ? '#6365E0' : '#8898B6' }">
                  {{ state.edurz == 1 ? '(已认证)' : '(未认证)' }}
                </text>
              </view>
              <view class="row_value">
                <switch color="#6365E0" :checked="state.edurz == 1" data-name="edurz" @change="handleSwitchChange" />
              </view>
            </view>
          </view>
          <view class="rz_item">
            <view class="rz_title">婚况认证</view>
            <view class="id_rz_row flex flex_ac flex_jsb mb12">
              <view class="id_upload flex flex_dc flex_v" @click="handleUploadIdCard('marryimg')">
                <view v-if="stateString.marryimg" class="delete_icon flex flex_dc" @click.stop="handleDeleteRz('marryimg')">
                  <text class="iconfont icon-guanbi2"></text>
                </view>
                <template v-if="!stateString.marryimg">
                  <view class="upload_file">点击上传</view>
                  <view class="upload_tips">婚况证件</view>
                </template>
                <image v-else :src="stateString.marryimg_url" mode="aspectFill" />
              </view>
            </view>
            <view class="form_item">
              <view class="row_title flex flex_ac" style="width: 260rpx">
                <text>认证状态</text>
                <text :style="{ color: state.marryrz == 1 ? '#6365E0' : '#8898B6' }">
                  {{ state.marryrz == 1 ? '(已认证)' : '(未认证)' }}
                </text>
              </view>
              <view class="row_value">
                <switch color="#6365E0" :checked="state.marryrz == 1" data-name="marryrz" @change="handleSwitchChange" />
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
      <view class="footer">
        <view class="small-btn confirm-small-btn" :class="{ disabled: isSubmitting }" @click="handleSubmit">
          <Loading v-if="isSubmitting" class="mr8" />
          保存
        </view>
      </view>
    </view>
  </uni-drawer>

  <!-- 单列 -->
  <PickerView v-if="pickerViewKey" ref="pickerView" v-model="state[pickerViewKey]" :options="pickerViewOptions" :title="pickerViewTitle" @confirm="handleConfirmPickerView" />

  <!-- 多列 -->
  <PickerView
    v-if="pickerViewKey2"
    ref="pickerView2"
    v-model="condInterval[pickerViewKey2]"
    :options="pickerViewOptions2"
    :title="pickerViewTitle2"
    :columns="2"
    @confirm="handleConfirmPickerView2"
  />

  <!-- 多选 -->
  <Picker ref="multiplePicker" v-model="stateCondMore[pickerKey]" :options="pickerOptions" multiple :title="pickerTitle" @confirm="handleConfirmPicker" />

  <Area v-if="areaKey" ref="area" v-model="areaSearch[areaKey].area" :title="areaTitle" @confirm="handleConfirmArea" />

  <DatePicker v-model="formatAgeText" title="选择年龄" ref="datePicker" @confirm="handleDateConfirm" />
</template>

<script setup>
import { ref, reactive, onMounted, defineEmits, computed, getCurrentInstance, nextTick, watch } from 'vue';
import { useStore } from 'vuex';

import InputForm from '@/components/Form/InputForm';
import EasyTextarea from '@/oeui/new/EasyTextarea';
import Picker from '@/oeui/new/Picker';
import PickerView from '@/oeui/new/PickerView';
import Area from '@/oeui/new/Area';
import DatePicker from '@/oeui/new/BirthdayDatePicker';
import GroupButton from '@/oeui/new/GroupButton';
import ButtonSelector from '@/oeui/new/ButtonSelector';
import Loading from '@/oeui/new/Loading';

import { getPickerDistTxt } from '@/utils/utils.js';
import { chooseAndUploadImage } from '@/utils/upload.js';
import { verifyWx } from '@/api/common.js';
import { editCrmmember, userDetail, postPhoto } from '@/api/total.js';
import { genderOptions } from '@/assets/staticData';

import avatar_d from '@/assets/svg/avatar_d.svg';
import successIcon from '@/assets/svg/success.svg';

const { proxy } = getCurrentInstance();

const emit = defineEmits(['search']);

const drawer = ref(null);
const store = useStore();
const config = store.state?.config;

let pickerViewOptions = [];
let pickerViewTitle = ref('');
let pickerViewKey = ref('');

let pickerViewOptions2 = [];
let pickerViewTitle2 = ref('');
let pickerViewKey2 = ref('');

let pickerOptions = [];
let pickerTitle = ref('');
let pickerKey = ref('');

let areaTitle = ref('');
let areaKey = ref('');

const isSubmitting = ref(false);

const userId = ref('');

const picker = reactive({
  crm_fromcat: [], //数据来源
  district: [],
  hometown: [],
  gender: [],
  salary: [],
  education: [],
  age: [],
  national: [], //民族
  faith: [], //信仰
  marry: [], //婚况
  job: [],
  party: [],
  activity: [],
  crmimport: [],
  makerdata: [],
  maytype: [],
  lunar: [],
  astro: [],
  blood: [], //血型
  height: [], //身高
  weight: [], //体重
  house: [],
  car: [],
  child: [],
  smoking: [],
  drinking: [],
  companytype: [],
  interest: [],
  language: [],

  // 编辑
  flag: [
    { text: '正常', value: 1 },
    { text: '隐藏资料', value: 10 }
  ],
  crm_servlevel: [],
  sort: [],
  health: [],
  parentstatus: [],
  parenteconomic: [],
  parentshealthcare: [],
  fatherwork: [],
  motherwork: [],
  tophome: [],
  allopatry: [],
  focus: [],
  wedding: [],
  cooking: [],
  housework: [],
  liveparent: [],
  rest: [],
  wantchild: []
});

let areaSearch = reactive({
  district: { area: {}, ids: [] },
  cond_district: { area: {}, ids: [] },
  hometown: { area: {}, ids: [] }
});

const state = reactive({
  maytype: '',
  gender: '',
  ageyear: '',
  agemonth: '',
  ageday: '',
  lunar: '',
  astro: '',
  marry: '',
  dist1: '',
  dist2: '',
  dist3: '',
  dist4: '',
  home1: '',
  home2: '',
  home3: '',
  home4: '',
  height: '',
  weight: '',
  education: '',
  job: '',
  salary: '',
  flag: '',
  grade: '',
  fromcat: '',
  willtype: '',
  servlevel: '',

  national: '',
  faith: '',
  blood: '',
  sort: '',
  car: '',
  house: '',
  child: '',
  health: '',
  smoking: '',
  drinking: '',
  companytype: '',
  parentstatus: '',
  parenteconomic: '',
  parentshealthcare: '',
  fatherwork: '',
  motherwork: '',
  tophome: '',
  allopatry: '',
  focus: '',
  wedding: '',
  cooking: '',
  housework: '',
  liveparent: '',
  rest: '',
  wantchild: '',

  idrz: 0,
  houserz: 0,
  carrz: 0,
  edurz: 0,
  marryrz: 0
});

const stateString = reactive({
  mobileaddr: '',
  mobileisp: '',
  weixin: '',
  school: '',
  othermobi: '',
  nickname: '',
  truename: '',
  headimg: '',
  headimg_url: '',
  mobile: '',
  company: '',
  idnumber: '',
  intro: '',
  remark: '',

  paper1: '',
  paper2: '',
  paper3: '',
  houseimg: '',
  carimg: '',
  eduimg: '',
  marryimg: '',

  paper1_url: '',
  paper2_url: '',
  paper3_url: '',
  houseimg_url: '',
  carimg_url: '',
  eduimg_url: '',
  marryimg_url: ''
});

const stateCond = reactive({
  age1: '',
  age2: '',
  area1: '',
  area2: '',
  area3: '',
  height1: '',
  height2: '',
  weight1: '',
  weight2: '',
  other: ''
});

const stateCondMore = reactive({
  cond_astro: '',
  cond_car: '',
  cond_child: '',
  cond_education: '',
  cond_house: '',
  cond_lunar: '',
  cond_marry: '',
  cond_salary: '',
  interest: '',
  language: ''
});

const condInterval = reactive({
  condAges: [stateCond.age1, stateCond.age2],
  height: [stateCond.height1, stateCond.height2],
  weight: [stateCond.weight1, stateCond.weight2]
});

const photoList = ref([]); // 客户资料附件
const userphoto = ref([]); // 客户线上相册

const filteredGenderOptions = computed(() => {
  return genderOptions.filter(option => option.value !== '');
});

const formatAgeText = computed(() => {
  if (!state.ageyear) return '';
  const padZero = num => num.toString().padStart(2, '0');
  const month = state.agemonth ? padZero(state.agemonth) : '00';
  const day = state.ageday ? padZero(state.ageday) : '00';
  return `${state.ageyear}-${month}-${day}`;
});

// 验证微信号
const showWeixintips = ref(false);
const wxmsg = ref('');
const handleVerifyWeixin = async () => {
  showWeixintips.value = false;
  if (!stateString.weixin) return;

  try {
    const res = await verifyWx({ weixin: stateString.weixin });
    if (res.ret !== 1) {
      showWeixintips.value = true;
      wxmsg.value = res.msg;
    } else {
      showWeixintips.value = false;
      wxmsg.value = '';
    }
  } catch (error) {
    console.error(error);
  }
};

const handleInputWeixin = () => {
  if (stateString.weixin.length < 30) {
    showWeixintips.value = false;
  }
};

const handleClearWeixin = () => {
  wxmsg.value = '';
  showWeixintips.value = false;
};

const handleSwitchChange = e => {
  const { value } = e.detail;
  const { name } = e.target.dataset;
  state[name] = value ? 1 : 0;
};

const handleDateConfirm = ({ text, date }) => {
  state.ageyear = date.year;
  state.agemonth = date.month || '';
  state.ageday = date.day || '';
};

// 上传头像
const handleUploadAvatar = async () => {
  try {
    const result = await chooseAndUploadImage();
    stateString.headimg = result.drawimg;
    stateString.headimg_url = result.drawimg_url;
  } catch (error) {
    console.error('头像上传失败:', error);
  }
};

// 上传认证图片
const handleUploadIdCard = async type => {
  try {
    const result = await chooseAndUploadImage();
    stateString[type] = result.drawimg;
    stateString[`${type}_url`] = result.drawimg_url;
  } catch (error) {
    console.error('图片上传失败:', error);
  }
};

// 删除认证图片
const handleDeleteRz = type => {
  stateString[type] = '';
};

// 添加相册
const addPhoto = async photo => {
  if (!photo || !photo.drawimg) return;

  try {
    const res = await postPhoto({
      a: 'saveadd',
      mid: userId.value,
      thumbimg: photo.thumbimg,
      drawimg: photo.drawimg
    });

    if (res.ret === 1) {
      uni.showToast({
        title: '已更新',
        icon: 'none',
        image: successIcon
      });

      if (photo.index !== undefined && photoList.value[photo.index]) {
        photoList.value[photo.index].albumid = res.result;
      }
    } else {
      uni.showToast({ title: res.msg || '添加失败', icon: 'none' });
    }
  } catch (error) {
    console.error(error);
  }
};

// 删除相册
const deletePhoto = async photo => {
  if (!photo || !photo.albumid) return;

  try {
    const res = await postPhoto({ a: 'del', id: photo.albumid });
    if (res.ret === 1) {
      uni.showToast({
        title: '已删除',
        icon: 'none',
        image: successIcon
      });
      return true;
    } else {
      uni.showToast({ title: res.msg || '删除失败', icon: 'none' });
    }
  } catch (error) {
    console.error(error);
  }
};

const handleUploadPhoto = async () => {
  try {
    const result = await chooseAndUploadImage({ module: 'upload' });
    result.index = photoList.value.length;
    photoList.value.push(result);
    await addPhoto(result);
  } catch (error) {
    console.error(error);
  }
};

const handleDeletePhoto = async (index, item) => {
  proxy.$showModal({
    title: '温馨提示',
    content: `确定要删除当前相册吗？`,
    success: async res => {
      if (res.confirm) {
        try {
          if (item.albumid) {
            await deletePhoto(item);
          }

          photoList.value.splice(index, 1);
        } catch (error) {
          console.error(error);
        }
      }
    }
  });
};

const previewImage = (list, index) => {
  uni.previewImage({
    urls: list.map(item => item.drawimg_url),
    current: list[index].drawimg_url
  });
};

const showArea = async (title, key) => {
  areaTitle.value = title;
  areaKey.value = key;
  await nextTick();
  setTimeout(() => {
    proxy.$refs.area.open(key);
  }, 0);
};

const showDatePicker = () => {
  proxy.$refs.datePicker.open();
};

// 处理显示PickerView弹窗
const showPickerView = async (title, optionsKey, key) => {
  const OPTIONS = getOptionsByKey(optionsKey);
  pickerViewKey.value = key;
  pickerViewTitle.value = title;
  pickerViewOptions = OPTIONS;
  await nextTick();
  setTimeout(() => {
    proxy.$refs.pickerView.open();
  }, 0);
};

// 处理显示多列PickerView弹窗
const showPickerView2 = async (title, optionsKey, key) => {
  const OPTIONS = getOptionsByKey(optionsKey);
  pickerViewKey2.value = key;
  pickerViewTitle2.value = title;
  pickerViewOptions2 = OPTIONS;
  await nextTick();
  setTimeout(() => {
    proxy.$refs.pickerView2.open();
  }, 0);
};

watch(
  () => pickerViewKey2.value,
  newValue => {
    const VALUES = {
      condAges: [stateCond.age1, stateCond.age2],
      height: [stateCond.height1, stateCond.height2],
      weight: [stateCond.weight1, stateCond.weight2]
    };
    condInterval[newValue] = VALUES[newValue];
  },
  { deep: true }
);

// 处理显示Picker单选、多选弹窗
const showPicker = (title, optionsKey, key) => {
  const OPTIONS = getOptionsByKey(optionsKey);
  pickerKey.value = key;
  pickerTitle.value = title;
  pickerOptions = OPTIONS;
  proxy.$refs.multiplePicker.open();
};

const handleConfirmPickerView = e => {
  state[pickerViewKey.value] = e.value;
};

const handleConfirmPicker = e => {
  stateCondMore[pickerKey.value] = e.map(item => item.value).join(',');
};

const handleConfirmPickerView2 = e => {
  const values = JSON.parse(JSON.stringify(e)).map(item => item?.value ?? null);
  const [value1, value2] = values;

  const keyMap = {
    condAges: ['age1', 'age2'],
    height: ['height1', 'height2'],
    weight: ['weight1', 'weight2']
  };

  const [prop1, prop2] = keyMap[pickerViewKey2.value] || [];
  if (prop1 && prop2) {
    stateCond[prop1] = value1;
    stateCond[prop2] = value2;
    condInterval[pickerViewKey2.value] = values;
  }
};

// 选择地区确认处理
const handleConfirmArea = e => {
  areaSearch[areaKey.value] = {
    area: e?.area || {},
    ids: e?.ids || []
  };

  const mapping = {
    district: {
      dist1: e?.area?.province?.value,
      dist2: e?.area?.city?.value,
      dist3: e?.area?.district?.value,
      dist4: ''
    },
    hometown: {
      home1: e?.area?.province?.value,
      home2: e?.area?.city?.value,
      home3: e?.area?.district?.value,
      home4: ''
    },
    cond_district: {
      area1: e?.area?.province?.value,
      area2: e?.area?.city?.value,
      area3: e?.area?.district?.value
    }
  };

  const targetState = areaKey.value.includes('cond_') ? stateCond : state;
  Object.assign(targetState, mapping[areaKey.value] || {});
};

const getOptionText = (optionsKey, value) => {
  const OPTIONS_MAP = getOptionsByKey(optionsKey);
  if (!OPTIONS_MAP || !OPTIONS_MAP.length) return '';

  const foundItem = OPTIONS_MAP.find(item => item.value == value);
  return foundItem ? foundItem.text : '';
};

const formatSelectedText = (selectedValueStr, options) => {
  if (!selectedValueStr) return '';
  const selectedValues = selectedValueStr.split(',');
  const selectedTexts = selectedValues
    .map(value => {
      const found = options.find(option => Number(option.value) === Number(value));
      return found ? found.text : '';
    })
    .filter(text => text);
  return selectedTexts.join('、') || '';
};

const getOptionsByKey = key => {
  const OPTIONS_MAP = {
    crm_fromcat: picker.crm_fromcat,
    marry: picker.marry,
    job: picker.job,
    national: picker.national,
    faith: picker.faith,
    maytype: picker.maytype,
    lunar: picker.lunar,
    astro: picker.astro,
    blood: picker.blood,
    height: picker.height,
    weight: picker.weight,
    house: picker.house,
    car: picker.car,
    smoking: picker.smoking,
    drinking: picker.drinking,
    companytype: picker.companytype,
    condAges: [picker.age, picker.age],
    condHeights: [picker.height, picker.height],
    condWeights: [picker.weight, picker.weight],
    cond_education: picker.education,
    cond_salary: picker.salary,
    cond_house: picker.house,
    cond_car: picker.car,
    cond_child: picker.child,
    cond_lunar: picker.lunar,
    cond_astro: picker.astro,
    interest: picker.interest,
    language: picker.language,

    // 编辑
    flag: picker.flag,
    crm_servlevel: picker.crm_servlevel,
    sort: picker.sort,
    child: picker.child,
    health: picker.health,
    parentstatus: picker.parentstatus,
    parenteconomic: picker.parenteconomic,
    parentshealthcare: picker.parentshealthcare,
    fatherwork: picker.fatherwork,
    motherwork: picker.motherwork,
    tophome: picker.tophome,
    allopatry: picker.allopatry,
    focus: picker.focus,
    wedding: picker.wedding,
    cooking: picker.cooking,
    housework: picker.housework,
    liveparent: picker.liveparent,
    rest: picker.rest,
    wantchild: picker.wantchild
  };

  return OPTIONS_MAP[key] || [];
};

const formatCond = data => {
  let params = {};
  for (const key in data) {
    params['cond_' + key] = data[key];
  }
  return params;
};

// 地区组件数据回显
const formatAddressData = data => {
  const buildAddress = prefix => {
    const result = { area: {}, ids: [] };
    const levels = ['province', 'city', 'district'];

    levels.forEach((level, index) => {
      const num = index + 1;
      const value = data[`${prefix}${num}`];
      const text = data[`${prefix}${num}_t`];
      if (value && value !== '0' && text) {
        result.area[level] = { value, text };
      }
    });

    result.ids = [
      Object.values(result.area)
        .map(item => item.value)
        .filter(Boolean)
    ];
    return result.ids.length ? result : { area: {}, ids: [] };
  };

  areaSearch.district = buildAddress('dist');
  areaSearch.cond_district = buildAddress('area');
  areaSearch.hometown = buildAddress('home');
};

const validateForm = () => {
  const validationRules = [
    { condition: !stateString.nickname, message: '请填写昵称' },
    { condition: showWeixintips.value, message: wxmsg.value }
  ];

  for (const rule of validationRules) {
    if (rule.condition) {
      uni.showToast({ title: rule.message, icon: 'none' });
      return false;
    }
  }

  return true;
};

const handleSubmit = async () => {
  if (isSubmitting.value) return;

  if (!validateForm()) return;

  isSubmitting.value = true;

  try {
    const params = {
      id: userId.value,
      ...state,
      ...stateString,
      ...stateCondMore,
      ...formatCond(stateCond)
    };

    const res = await editCrmmember(params);
    if (res.ret === 1) {
      uni.showToast({
        title: '保存成功',
        icon: 'none',
        image: successIcon
      });
      back();
    } else {
      uni.showToast({ title: res.msg, icon: 'none' });
    }
  } catch (error) {
    console.error(error);
  } finally {
    isSubmitting.value = false;
  }
};

const back = () => {
  drawer.value.close();
  emit('search');
};

const getUserDetail = async () => {
  try {
    const res = await userDetail({ id: userId.value });
    if (res.ret === 1) {
      const data = res.result.data;
      const cond = res.result.cond;

      Object.keys(state).forEach(key => {
        state[key] = data[key];
      });

      Object.keys(stateString).forEach(key => {
        stateString[key] = data[key];
      });

      Object.keys(stateCond).forEach(key => {
        stateCond[key] = cond[key];
      });

      Object.keys(stateCondMore).forEach(key => {
        const targetKey = key.startsWith('cond_') ? key.substring(5) : key;
        if (!['interest', 'language'].includes(targetKey)) {
          stateCondMore[key] = cond[targetKey];
        } else {
          stateCondMore[key] = data[targetKey];
        }
      });

      // 客户资料附件
      photoList.value = res.result?.photo ?? [];

      // 客户线上相册
      if (data.userid) {
        userphoto.value = res.result?.user_photo;
      }

      // 地区数据回显
      formatAddressData({ ...data, ...cond });
    }
  } catch (error) {
    console.error(error);
  }
};

const initPicker = () => {
  for (const key in picker) {
    try {
      const storedValue = uni.getStorageSync(key);
      if (storedValue) {
        picker[key] = JSON.parse(storedValue);
      } else {
        if (key === 'flag') continue;
        picker[key] = [];
      }
    } catch (error) {
      picker[key] = [];
    }
  }
};

const open = data => {
  userId.value = data?.mid;
  if (userId.value) {
    getUserDetail();
  }

  drawer.value.open();
};

onMounted(() => {
  initPicker();
});

defineExpose({
  open,
  close: () => drawer.value.close()
});
</script>

<style lang="scss" scoped>
.member_content {
  .scroll-view-box {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .form_box {
    padding: 20rpx 32rpx;

    .required::after {
      content: '*';
      color: #f53f3f;
      margin-left: 4rpx;
    }

    .module_title {
      font-size: 36rpx;
      font-weight: 550;
      color: #000;
    }

    .row_title {
      width: 160rpx;
      padding-right: 24rpx;
      font-size: 32rpx;
      color: #000;
      flex-shrink: 0;
    }

    .avatar {
      .avatar_seat {
        position: relative;

        .upload_file {
          position: absolute;
          top: 0;
          width: 48rpx;
          height: 48rpx;
          z-index: 2;
          opacity: 0;
        }

        image {
          width: 48rpx;
          height: 48rpx;
          display: block;
          border-radius: 4rpx;
        }
      }
    }

    .mobile_error {
      color: #f53f3f;
      font-size: 24rpx;
      text-align: right;
      line-height: 1;
    }

    .form_button_item {
      align-items: flex-start !important;
      flex-direction: column;
      justify-content: flex-start;
      height: auto !important;
      margin-bottom: 16rpx !important;
    }

    .form_item {
      height: 84rpx;
      @include flex-align-center;
      @include flex-between;
      margin-bottom: 8rpx;

      .row_value {
        flex: 1;
        @include flex-align-center;
        justify-content: flex-end;
        font-size: 32rpx;
        min-width: 0;

        .picker_no.picker_text {
          font-size: 32rpx;
        }

        .picker_no {
          color: #c0c4cc;
        }

        .picker_text {
          color: #666;
        }

        .value-container {
          flex: 1;
          min-width: 0;
          @include flex-align-center;
          justify-content: flex-end;
        }
      }

      .iconfont {
        font-size: 32rpx;
        color: #666;
        flex-shrink: 0;
        margin-left: 12rpx;
      }
    }

    .upload_container {
      .upload_annex {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -12rpx;
      }

      .upload_box {
        width: calc(25% - 24rpx);
        height: 0;
        aspect-ratio: 1/1;
        padding-bottom: calc(25% - 24rpx);
        margin: 12rpx;
        position: relative;
        border-radius: 16rpx;
        background-color: #f7f7f9;

        .icon-jiahao {
          font-size: 72rpx;
          color: #d6d6d6;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }

        .delete_icon {
          width: 38rpx;
          height: 38rpx;
          border-radius: 9999rpx;
          position: absolute;
          top: -10rpx;
          right: -10rpx;
          z-index: 100;
          background: rgba($color: #000000, $alpha: 0.8);

          .icon-guanbi2 {
            font-size: 24rpx;
            color: #fff;
          }
        }

        image {
          position: absolute;
          width: 100%;
          height: 100%;
          display: block;
          top: 0;
          left: 0;
          object-fit: cover;
          border-radius: 16rpx;
        }
      }
    }

    .rz_item {
      margin-bottom: 48rpx;
      padding-bottom: 24rpx;
      border-bottom: 1rpx solid #f7f7f9;

      &:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
      }

      .rz_title {
        color: #000;
        font-size: 32rpx;
      }

      .id_rz_row {
        margin-top: 24rpx;

        .id_upload {
          width: 200rpx;
          height: 136rpx;
          background-color: #f7f7f9;
          border-radius: 12rpx;
          position: relative;

          .delete_icon {
            width: 38rpx;
            height: 38rpx;
            border-radius: 9999rpx;
            position: absolute;
            top: -10rpx;
            right: -10rpx;
            z-index: 100;
            background: rgba($color: #000000, $alpha: 0.8);

            .icon-guanbi2 {
              font-size: 24rpx;
              color: #fff;
            }
          }

          .upload_file {
            font-size: 28rpx;
            color: #000;
            margin-bottom: 8rpx;
          }

          .upload_tips {
            font-size: 24rpx;
            color: #8898b6;
          }

          image {
            width: 100%;
            height: 100%;
            display: block;
            border-radius: 12rpx;
          }
        }
      }
    }
  }

  .footer {
    position: fixed;
    bottom: 120rpx;
    left: 50%;
    transform: translateX(-50%);
    height: 110rpx;
    @include flex-center;
    z-index: 101;

    .small-btn {
      @include flex-center;
      width: 240rpx;
      height: 80rpx;
      background: #6365e0;
      color: #fff;
      font-size: 32rpx;
      border-radius: 40rpx;
      box-shadow: 0rpx 16rpx 48rpx 0rpx rgba(27, 30, 223, 0.42);

      &:active {
        background: #8284e6;
      }

      &.disabled {
        background: #8284e6;
        opacity: 0.8;
        pointer-events: none;
      }
    }
  }
}
</style>

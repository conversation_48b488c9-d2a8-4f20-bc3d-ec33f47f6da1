<template>
  <PopupContainer ref="popup" titlePosition="center" title="放入公海" @close="close" @confirm="handleConfirm">
    <template #content>
      <view class="operate_form">
        <view class="form_item">
          <view class="row_title required">放弃原因</view>
          <view class="row_value" @click="showPickerView('选择放弃原因', 'crm_giveupcat', 'giveupcat')">
            <span v-if="giveupcatForm.giveupcat" class="picker_text">{{ getOptionText('crm_giveupcat', giveupcatForm.giveupcat) }}</span>
            <span v-else class="picker_no">请选择</span>
          </view>
          <view class="iconfont icon-youjiantou1"></view>
        </view>
        <view class="form_button_item">
          <view class="row_title" style="margin-bottom: 24rpx">备注信息</view>
          <EasyTextarea v-model="giveupcatForm.remark" :isBackground="false" :max="2000" placeholder="请输入" />
        </view>
      </view>
    </template>
  </PopupContainer>

  <PickerView ref="pickerView" v-model="giveupcatForm[pickerViewKey]" :options="pickerViewOptions" :title="pickerViewTitle" @confirm="handleConfirmPickerView" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance, defineProps, defineEmits, defineExpose, onMounted } from 'vue';
import PopupContainer from '@/oeui/new/PopupContainer';
import PickerView from '@/oeui/new/PickerView';
import EasyTextarea from '@/oeui/new/EasyTextarea';
import { getSaleSaleUserList } from '@/api/sale.js';

const { proxy } = getCurrentInstance();

const userId = ref('');

let giveupcatForm = ref({
  giveupcat: '',
  remark: ''
});

const picker = reactive({
  crm_giveupcat: []
});

let pickerViewOptions = [];
let pickerViewTitle = ref('');
let pickerViewKey = ref('');

const emit = defineEmits(['confirm']);

const popup = ref(null);

// 处理显示PickerView弹窗
const showPickerView = (title, optionsKey, key) => {
  const OPTIONS = getOptionsByKey(optionsKey);
  pickerViewKey.value = key;
  pickerViewTitle.value = title;
  pickerViewOptions = OPTIONS;
  proxy.$refs.pickerView.open();
};

const handleConfirmPickerView = e => {
  giveupcatForm[pickerViewKey.value] = e.value;
};

const getOptionsByKey = key => {
  const OPTIONS_MAP = {
    crm_giveupcat: picker.crm_giveupcat
  };

  return OPTIONS_MAP[key] || [];
};

const getOptionText = (optionsKey, value) => {
  const OPTIONS_MAP = getOptionsByKey(optionsKey);
  if (!OPTIONS_MAP || !OPTIONS_MAP.length) return '';

  const foundItem = OPTIONS_MAP.find(item => item.value == value);
  return foundItem ? foundItem.text : '';
};

const initForm = () => {
  giveupcatForm = reactive({
    giveupcat: '',
    remark: ''
  });
};

const initPicker = () => {
  for (const key in picker) {
    try {
      const storedValue = uni.getStorageSync(key);
      if (storedValue) {
        picker[key] = JSON.parse(storedValue);
      }
    } catch (error) {
      picker[key] = [];
    }
  }
};

const handleConfirm = async () => {
  if (!giveupcatForm.giveupcat) {
    uni.showToast({
      title: '请选择放弃原因',
      icon: 'none'
    });
    return;
  }

  try {
    const res = await getSaleSaleUserList({ a: 'savegiveup', id: userId.value, ...giveupcatForm });
    if (res.ret === 1) {
      uni.showToast({
        title: '操作成功',
        icon: 'none',
        image: '/assets/svg/success.svg'
      });
      emit('confirm', giveupcatForm);
    } else {
      uni.showToast({ title: res.msg, icon: 'none' });
    }
  } catch (error) {
    console.error(error);
  } finally {
    close();
  }
};

onMounted(() => {
  initPicker();
});

const open = id => {
  if (!id) return;
  initForm();
  userId.value = id;
  popup.value?.open('bottom');
};

const close = () => {
  popup.value?.close();
};

defineExpose({
  open,
  close
});
</script>

<style lang="scss" scoped>
.operate_form {
  @include flex-column;

  .form_button_item {
    align-items: flex-start !important;
    flex-direction: column;
    justify-content: flex-start;
    height: auto !important;
    margin-bottom: 16rpx !important;
  }

  .form_item {
    height: 84rpx;
    @include flex-align-center;
    @include flex-between;
    margin-bottom: 8rpx;

    .row_value {
      flex: 1;
      @include flex-align-center;
      justify-content: flex-end;
      font-size: 32rpx;
      min-width: 0;

      .picker_no.picker_text {
        font-size: 32rpx;
      }

      .picker_no {
        color: #c0c4cc;
      }

      .picker_text {
        color: #666;
      }

      .value-container {
        flex: 1;
        min-width: 0;
        @include flex-align-center;
        justify-content: flex-end;
      }
    }

    .iconfont {
      font-size: 32rpx;
      color: #666;
      flex-shrink: 0;
      margin-left: 12rpx;
    }
  }

  .required::after {
    content: '*';
    color: #f53f3f;
    margin-left: 4rpx;
  }

  .row_title {
    width: 160rpx;
    padding-right: 24rpx;
    font-size: 32rpx;
    color: #000;
    flex-shrink: 0;
  }
}
</style>

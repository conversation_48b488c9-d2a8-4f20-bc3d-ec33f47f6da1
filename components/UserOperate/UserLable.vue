<template>
  <Picker ref="lablePicker" v-model="tags" :options="pickerOptions" multiple title="批量选标签" @confirm="handleConfirmPicker" />
</template>

<script setup>
import { ref, computed, getCurrentInstance } from 'vue';
import { postMember } from '@/api/sale.js';
import Picker from '@/oeui/new/Picker';
import successIcon from '@/assets/svg/success.svg';

const { proxy } = getCurrentInstance();

const emit = defineEmits(['confirm']);

const userId = ref('');
const tags = ref('');

const pickerOptions = computed(() => {
  const tags = uni.getStorageSync('crm_tags');
  return tags ? JSON.parse(tags) : [];
});

const initTags = async () => {
  try {
    const res = await postMember({ a: 'tags', id: userId.value });
    if (res.ret === 1 && res.result.data.tags_arr.length) {
      const tagIds = res.result.data.tags_arr.map(item => item.tagid);
      tags.value = tagIds.join(',');
    }
  } catch (error) {
    console.error(error);
  }
};

const handleConfirmPicker = async e => {
  const ids = e.map(item => item.value).join(',');
  try {
    const res = await postMember({ a: 'savetags', id: userId.value, tags: ids });
    if (res.ret === 1) {
      uni.showToast({
        title: '操作成功',
        icon: 'none',
        image: successIcon
      });
      emit('confirm', ids);
    } else {
      uni.showToast({ title: res.msg, icon: 'none' });
    }
  } catch (error) {
    console.error(error);
  }
};

const open = id => {
  if (!id) return;
  userId.value = id;

  // 处理标签回显
  if (id.indexOf(',') == -1) {
    initTags();
  } else {
    tags.value = '';
  }

  proxy.$refs.lablePicker.open();
};

defineExpose({
  open
});
</script>

<style lang="scss" scoped></style>

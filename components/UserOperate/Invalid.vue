<template>
  <PopupContainer ref="popup" titlePosition="center" :title="title" @close="close" @confirm="handleConfirm">
    <template #content>
      <view class="operate_form">
        <view class="alert_box">
          <text class="iconfont icon-tips-fill"></text>
          设为{{ action == 'doinvalid' ? '无效资源' : '黑名单' }}后请输入原因，确定后该资源将从你的库中移入到回收站
        </view>
        <view class="form_item" v-if="info.mid">
          <view class="row_title">客户资源</view>
          <view class="row_value">
            <span class="picker_text">{{ info.name }}&nbsp;/&nbsp;资源ID:{{ info.mid }}</span>
          </view>
        </view>
        <view class="form_item">
          <view class="row_title required">选择原因</view>
          <view class="row_value" @click="showPickerView('选择原因', 'crm_recycle_cat', 'cat')">
            <span v-if="invalidForm.cat" class="picker_text">{{ getOptionText('crm_recycle_cat', invalidForm.cat) }}</span>
            <span v-else class="picker_no">请选择</span>
          </view>
          <view class="iconfont icon-youjiantou1"></view>
        </view>
        <view class="form_button_item">
          <view class="row_title" style="margin-bottom: 24rpx">备注信息</view>
          <EasyTextarea v-model="invalidForm.remark" :isBackground="false" :max="2000" placeholder="请输入" />
        </view>
      </view>
    </template>
  </PopupContainer>

  <PickerView ref="pickerView" v-model="invalidForm[pickerViewKey]" :options="pickerViewOptions" :title="pickerViewTitle" @confirm="handleConfirmPickerView" />
</template>

<script setup>
import { ref, reactive, getCurrentInstance, defineProps, defineEmits, defineExpose, onMounted } from 'vue';
import PopupContainer from '@/oeui/new/PopupContainer';
import PickerView from '@/oeui/new/PickerView';
import EasyTextarea from '@/oeui/new/EasyTextarea';
import { getSaleSaleUserList } from '@/api/sale.js';

const { proxy } = getCurrentInstance();

const userId = ref('');

const props = defineProps({
  title: {
    type: String,
    default: '标题'
  },
  action: {
    type: String,
    default: ''
  },
  info: {
    type: Object,
    default: () => {
      return {
        mid: '',
        name: ''
      };
    }
  }
});

let invalidForm = reactive({
  cat: '',
  remark: ''
});

const picker = reactive({
  crm_recycle_cat: []
});

let pickerViewOptions = [];
let pickerViewTitle = ref('');
let pickerViewKey = ref('');

const emit = defineEmits(['confirm']);

const popup = ref(null);

// 处理显示PickerView弹窗
const showPickerView = (title, optionsKey, key) => {
  const OPTIONS = getOptionsByKey(optionsKey);
  pickerViewKey.value = key;
  pickerViewTitle.value = title;
  pickerViewOptions = OPTIONS;
  proxy.$refs.pickerView.open();
};

const handleConfirmPickerView = e => {
  invalidForm[pickerViewKey.value] = e.value;
};

const getOptionsByKey = key => {
  const OPTIONS_MAP = {
    crm_recycle_cat: picker.crm_recycle_cat
  };

  return OPTIONS_MAP[key] || [];
};

const getOptionText = (optionsKey, value) => {
  const OPTIONS_MAP = getOptionsByKey(optionsKey);
  if (!OPTIONS_MAP || !OPTIONS_MAP.length) return '';

  const foundItem = OPTIONS_MAP.find(item => item.value == value);
  return foundItem ? foundItem.text : '';
};

const initForm = () => {
  invalidForm = reactive({
    cat: '',
    remark: ''
  });
};

const initPicker = () => {
  for (const key in picker) {
    try {
      const storedValue = uni.getStorageSync(key);
      if (storedValue) {
        picker[key] = JSON.parse(storedValue);
      }
    } catch (error) {
      picker[key] = [];
    }
  }
};

const handleConfirm = async () => {
  if (!invalidForm.cat) {
    uni.showToast({
      title: '请选择原因',
      icon: 'none'
    });
    return;
  }

  try {
    const res = await getSaleSaleUserList({ a: props.action, id: userId.value, ...invalidForm });
    if (res.ret === 1) {
      uni.showToast({
        title: '操作成功',
        icon: 'none',
        image: '/assets/svg/success.svg'
      });
      emit('confirm', invalidForm);
    } else {
      uni.showToast({ title: res.msg, icon: 'none' });
    }
  } catch (error) {
    console.error(error);
  } finally {
    close();
  }
};

onMounted(() => {
  initPicker();
});

const open = id => {
  if (!id) return;
  initForm();
  userId.value = id;
  popup.value?.open('bottom');
};

const close = () => {
  popup.value?.close();
};

defineExpose({
  open,
  close
});
</script>

<style lang="scss" scoped>
.operate_form {
  @include flex-column;

  .alert_box {
    padding: 24rpx;
    background-color: #fdf6ec;
    color: #e6a23c;
    border-radius: 16rpx;
    margin-bottom: 32rpx;
  }

  .form_button_item {
    align-items: flex-start !important;
    flex-direction: column;
    justify-content: flex-start;
    height: auto !important;
    margin-bottom: 16rpx !important;
  }

  .form_item {
    height: 84rpx;
    @include flex-align-center;
    @include flex-between;
    margin-bottom: 8rpx;

    .row_value {
      flex: 1;
      @include flex-align-center;
      justify-content: flex-end;
      font-size: 32rpx;
      min-width: 0;

      .picker_no.picker_text {
        font-size: 32rpx;
      }

      .picker_no {
        color: #c0c4cc;
      }

      .picker_text {
        color: #666;
      }

      .value-container {
        flex: 1;
        min-width: 0;
        @include flex-align-center;
        justify-content: flex-end;
      }
    }

    .iconfont {
      font-size: 32rpx;
      color: #666;
      flex-shrink: 0;
      margin-left: 12rpx;
    }
  }

  .required::after {
    content: '*';
    color: #f53f3f;
    margin-left: 4rpx;
  }

  .row_title {
    width: 160rpx;
    padding-right: 24rpx;
    font-size: 32rpx;
    color: #000;
    flex-shrink: 0;
  }
}
</style>

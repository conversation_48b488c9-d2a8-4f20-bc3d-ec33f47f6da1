<template #content>
  <view class="range_popup">
    <view class="range_type">
      <view class="range_type_item" :class="getRangeItemClass(item)" v-for="item in viewTypes" :key="item.value" @click="handleViewType(item)">
        {{ item.text }}
      </view>
    </view>

    <TreeView v-if="isClass" v-model="rangeForm.s_roleid" :tree="netpicker.depart" :disabled="treeDisabled" title="选择部门" @confirm="handleDepartment" />

    <Tree v-if="isSale" v-model="rangeForm.s_makerid" :tree="netpicker.maker" :disabled="treeDisabled" title="选择销售红娘" rowTitle="销售红娘" @confirm="handleSaleTree" />

    <Tree v-if="isAssist" v-model="rangeForm.s_helpmkid" :tree="netpicker.maker" :disabled="treeDisabled" title="选择协作红娘" rowTitle="协作红娘" @confirm="handleAssistTree" />

    <Tree v-if="isFace" v-model="rangeForm.s_facemkid" :tree="netpicker.maker" :disabled="treeDisabled" title="选择面谈红娘" rowTitle="面谈红娘" @confirm="handleFaceTree" />
  </view>
</template>
<script setup>
import { reactive, computed, watch, defineExpose } from 'vue';
import { useStore } from 'vuex';
import { usePermission } from '@/composables/usePermission';
import Tree from '@/oeui/new/Tree/Tree';
import { getClassAll, getCalssMaker } from '@/api/common.js';
import TreeView from '@/oeui/new/TreeView/TreeView';
const store = useStore();
const { hasPermission } = usePermission();
const props = defineProps({
  isClass: {
    type: Boolean,
    default: true
  },
  isSale: {
    type: Boolean,
    default: true
  },
  isAssist: {
    type: Boolean,
    default: false
  },
  isFace: {
    type: Boolean,
    default: false
  }
});

const hasWorkPower = hasPermission('work', 'work_view');
const hasMyWorkPower = hasPermission('mywork', 'mywork_view');

// 数据管理
const netpicker = reactive({
  depart: [],
  maker: []
});

const rangeForm = reactive({
  s_roleid: '', // 部门
  s_makerid: '', // 销售红娘
  s_facemkid: '', // 面谈红娘
  s_helpmkid: '', // 协作红娘
  viewtype: store.state?.loginInfo?.pwscope
});

const allViewTypes = Object.freeze([
  { text: '本人数据', value: '1' },
  { text: '部门数据', value: '2' },
  { text: '门店数据', value: '3' }
]);

const treeDisabled = computed(() => rangeForm.viewtype === '1');

const viewTypes = computed(() => {
  const { pwscope } = store.state.loginInfo;
  return allViewTypes.filter(item => {
    if (pwscope === '3') return true;
    if (pwscope === '2') return Number(item.value) <= 2;
    if (pwscope === '1') return item.value === '1';
    return false;
  });
});

const getRangeItemClass = computed(() => {
  return item => ({
    active: rangeForm.viewtype === item.value,
    disabled: (item.value === '1' && !hasMyWorkPower) || (['2', '3'].includes(item.value) && !hasWorkPower)
  });
});

const init = search => {
  setTimeout(() => {
    Object.assign(rangeForm, search);
  }, 500);
};

// 监听部门选择
const handleDepartment = node => {
  Object.assign(rangeForm, { s_roleid: node.roleid, s_makerid: '' });
  getMakerData(rangeForm.viewtype);
};

// 监听数据范围类型
const handleViewType = item => {
  if (item.value === '1' && !hasMyWorkPower) return;
  if (['2', '3'].includes(item.value) && !hasWorkPower) return;

  Object.assign(rangeForm, {
    viewtype: item.value,
    s_roleid: '',
    s_makerid: '',
    s_facemkid: '',
    s_helpmkid: ''
  });
};

const handleSaleTree = item => {
  Object.assign(rangeForm, { s_makerid: item.makerid });
};

const handleAssistTree = item => {
  Object.assign(rangeForm, { s_helpmkid: item.makerid });
};

const handleFaceTree = item => {
  Object.assign(rangeForm, { s_facemkid: item.makerid });
};

// 获取部门数据
const getClassData = async type => {
  const viewtype = type || rangeForm.viewtype;
  const a = viewtype === '2' ? 'mydepart' : 'sitedepart';
  const res = await getClassAll({ viewtype, a });
  if (res.ret === 1) {
    netpicker.depart = res.result.data.map(item => ({ first: 1, ...item }));
  }
};

// 获取运营人员数据
const getMakerData = async type => {
  const viewtype = type || rangeForm.viewtype;
  const res = await getCalssMaker({ viewtype, s_roleid: rangeForm.s_roleid });
  if (res.ret === 1) {
    netpicker.maker = res.result.data;
  }
};

watch(
  () => rangeForm.viewtype,
  async newRange => {
    if (newRange.viewtype === '1') return;
    if (props.isClass) await getClassData(newRange.viewtype);
    if (props.isSale) await getMakerData(newRange.viewtype);
  },
  { immediate: true, deep: true }
);

defineExpose({
  rangeForm,
  init
});
</script>

<style lang="scss" scoped>
.range_popup {
  padding: 16rpx 0 0;

  .range_type {
    @include flex-wrap;
    margin-bottom: 20rpx;

    &_item {
      padding: 12rpx 24rpx;
      border-radius: 16rpx;
      background-color: #f7f7f9;
      color: #000;
      font-size: 28rpx;
      text-align: center;
      transition: all 0.1s;
      box-sizing: border-box;
      border: 1rpx solid transparent;
      margin-right: 24rpx;

      &.active {
        background: #f3f3fd;
        color: #6365e0;
        border: 1rpx solid #6365e0;
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}
</style>
